<script setup lang="ts">
  import { BasicModal, useModalInner } from '@hiip/internal/components/Modal';
  import { useMessage } from '@hiip/internal/hooks/web/useMessage';
  import { DatePicker, Divider, Form, Input, Upload } from 'ant-design-vue';
  import type { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface';
  import { useToggle } from '@vueuse/core';
  import { nextTick, ref } from 'vue';
  import { Button } from '@hiip/internal/components/Button';
  import { MarkdownRender } from '@hiip/internal/components/MarkdownRender';
  import { ApiSelect } from '@hiip/internal/components/Form';
  import { Icon } from '@hiip/internal/components/Icon';
  import type { ModalEditDT } from './data';
  import { getProvinceDict, importPdf } from '/@/api/metric';

  // 导入图片资源
  import iPdfIcon from '/@/assets/images/i-pdf.png';
  import iExcelIcon from '/@/assets/images/i-excel.png';
  import aiBotIcon from '/@/assets/images/ai-bot.png';

  const { createConfirm } = useMessage();

  const emit = defineEmits(['register', 'sussess', 'minimize']);

  const [registerModal, { closeModal: closeModal }] = useModalInner<ModalEditDT>((_data) => {});

  // 表单数据
  const formData = ref({
    standardName: '三甲评审',
    version: '2025',
    province: '',
  });

  async function handleOk() {
    emit('sussess');
    closeModal();
  }

  // 最小化处理函数
  function handleMinimize() {
    emit('minimize');
  }

  const [_loading, setLoading] = useToggle(false);

  // 流式处理状态变量
  const messages = ref<string>('');
  const lastProcessedLength = ref(0);
  const isUploading = ref(false);
  const abortController = ref<AbortController | null>(null);
  const messageHistoryRef = ref<HTMLElement | null>(null);

  async function getProvinceOptions() {
    const {
      data: { data: list },
    } = await getProvinceDict();
    return list?.map((item) => ({
      label: item,
      value: item,
    }));
  }

  // 解析单行数据
  function parseDataLine(line: string): string | null {
    if (!line.trim()) return null;

    // 解析 "data: {"content":"消息内容"}" 格式
    if (line.startsWith('data: ')) {
      try {
        const jsonStr = line.substring(6); // 移除 "data: " 前缀
        const jsonData = JSON.parse(jsonStr);
        return jsonData.content || null;
      } catch (e) {
        console.error('JSON解析错误:', e);
        return null;
      }
    }
    return null;
  }

  // 处理流式数据
  function processStreamData(chunk: string) {
    const newChunk = chunk.substring(lastProcessedLength.value);
    lastProcessedLength.value = chunk.length;

    // 按行分割数据
    const lines = newChunk.split('\n');
    for (const line of lines) {
      const content = parseDataLine(line);
      if (content) {
        messages.value += content;

        // 自动滚动到底部
        nextTick(() => {
          if (messageHistoryRef.value) {
            messageHistoryRef.value.scrollTop = messageHistoryRef.value.scrollHeight;
          }
        });
        emit('sussess');
      }
    }
  }

  // 取消上传功能
  function cancelUpload() {
    if (abortController.value) {
      abortController.value.abort();
      abortController.value = null;
    }
    isUploading.value = false;
    setLoading(false);
    messages.value += '用户已取消\n';
  }

  function customRequest(options: UploadRequestOption) {
    const formData = new FormData();
    formData.append('file', options.file);

    // 重置状态
    messages.value = '';
    lastProcessedLength.value = 0;
    isUploading.value = true;
    setLoading(true);

    // 创建AbortController用于取消请求
    abortController.value = new AbortController();

    importPdf(formData, {
      signal: abortController.value.signal,
      onDownloadProgress: (progressEvent) => {
        const chunk = progressEvent.event.target.response;
        if (chunk) {
          processStreamData(chunk);
        }
      },
    })
      .then(() => {
        messages.value += 'PDF上传和处理完成！\n';
      })
      .catch((error) => {
        if (error.name === 'AbortError') {
          messages.value += '用户已取消\n';
        } else {
          console.error('上传错误:', error);
        }
      })
      .finally(() => {
        isUploading.value = false;
        setLoading(false);
        abortController.value = null;
      });
  }

  async function handleClose() {
    if (!isUploading.value) return true;
    let isConfirm = false;
    createConfirm({
      title: '提示',
      iconType: 'warning',
      content: '关闭后将取消处理，您确定吗？',
      onOk: () => {
        isConfirm = true;
        cancelUpload();
      },
    });
    return isConfirm;
  }
</script>

<template>
  <BasicModal
    wrap-class-name="import-standard-file-modal"
    @register="registerModal"
    :can-fullscreen="false"
    :destroy-on-close="false"
    :width="800"
    :draggable="false"
    :min-height="600"
    :use-wrapper="false"
    centered
    @ok="handleOk"
    :show-cancel-btn="false"
    :show-ok-btn="false"
    :closeFunc="handleClose"
    :closable="false"
  >
    <template #title>
      <div class="flex items-center justify-between w-full">
        <span>导入标准文件</span>
        <div class="flex items-center">
          <Button type="link">导入记录</Button>
          <Divider type="vertical" />
          <Icon icon="ant-design:close-outlined" class="text-2xl" />
        </div>
      </div>
    </template>
    <div class="import-modal-content">
      <!-- 表单区域 -->
      <Form :model="formData">
        <div class="form-section mb-6">
          <div class="form-row">
            <div class="form-item">
              <label class="form-label">标准名称</label>
              <Form.Item name="standardName">
                <Input
                  v-model:value="formData.standardName"
                  placeholder="请输入"
                  class="form-input"
                />
              </Form.Item>
            </div>
            <div class="form-item">
              <label class="form-label">版本</label>
              <Form.Item name="version">
                <DatePicker
                  v-model:value="formData.version"
                  mode="year"
                  value-format="YYYY"
                  format="YYYY"
                  placeholder="请选择"
                  class="form-input"
                />
              </Form.Item>
            </div>
            <div class="form-item">
              <label class="form-label">省份</label>
              <ApiSelect
                :api="getProvinceOptions"
                v-model:value="formData.province"
                placeholder="请选择"
                class="form-input"
                showSearch
                option-filter-prop="label"
              />
            </div>
          </div>
        </div>
      </Form>

      <!-- 文件上传区域 -->
      <div class="upload-section mb-6">
        <Upload
          class="upload-area"
          :accept="'.pdf,.xls,.xlsx'"
          :custom-request="customRequest"
          :disabled="isUploading"
          :show-upload-list="false"
        >
          <div class="upload-content">
            <div class="file-icons">
              <img :src="iPdfIcon" alt="PDF" class="file-icon" />
              <img :src="iExcelIcon" alt="Excel" class="file-icon" />
            </div>
            <div class="upload-text">
              <div class="upload-title">点击或将文件拖拽到此处上传</div>
              <div class="upload-desc">支持扩展名：pdf、xls、xlsx（文件大小不超过10MB）</div>
            </div>
          </div>
        </Upload>
      </div>

      <!-- AI思考过程区域 -->
      <div class="ai-process-section" v-if="isUploading">
        <div class="ai-header">
          <img :src="aiBotIcon" alt="AI Bot" class="ai-bot-icon" />
          <span class="ai-title">AI思考过程</span>
        </div>

        <!-- 消息显示区域 -->
        <div class="message-container">
          <div ref="messageHistoryRef" class="message-history">
            <MarkdownRender :content="messages" />
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons" v-if="isUploading">
        <Button type="primary" ghost @click="handleMinimize">最小化</Button>
        <Button type="primary" @click="handleOk">确定</Button>
        <Button @click="cancelUpload">取消</Button>
      </div>
    </div>
  </BasicModal>
</template>

<style lang="scss">
  .import-standard-file-modal {
    .ant-modal-header {
      background: transparent;
    }
    .ant-modal-content {
      background: linear-gradient(
        180deg,
        rgba(225, 237, 253, 1),
        rgba(255, 255, 255, 1) 20%,
        rgba(255, 255, 255, 1) 100%
      );
    }
  }

  .import-modal-content {
    padding: 0 24px 24px;

    // 表单区域样式
    .form-section {
      .form-row {
        display: flex;
        gap: 24px;
        align-items: flex-end;
      }

      .form-item {
        flex: 1;

        .form-label {
          display: block;
          margin-bottom: 8px;
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }

        .form-input {
          width: 100%;
        }
      }
    }

    // 文件上传区域样式
    .upload-section {
      .upload-area {
        width: 100%;

        .ant-upload {
          width: 100%;
        }

        .upload-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 44px 20px;
          border: 2px dashed #d9d9d9;
          border-radius: 8px;
          background: rgba(244, 249, 255, 1);
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            border-color: #1890ff;
          }

          .file-icons {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;

            .file-icon {
              width: 48px;
              height: 48px;
              object-fit: contain;
            }
          }

          .upload-text {
            text-align: center;

            .upload-title {
              font-size: 16px;
              color: #333;
              margin-bottom: 8px;
              font-weight: 500;
            }

            .upload-desc {
              font-size: 14px;
              color: #999;
            }
          }
        }
      }
    }

    // AI思考过程区域样式
    .ai-process-section {
      background: #f8fbff;
      border: 1px solid #e1edfd;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;

      .ai-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .ai-bot-icon {
          width: 24px;
          height: 24px;
          margin-right: 8px;
        }

        .ai-title {
          font-size: 16px;
          font-weight: 600;
          color: #1890ff;
        }
      }

      .message-container {
        .message-history {
          min-height: 200px;
          max-height: 300px;
          overflow-y: auto;
          border-radius: 6px;
          padding: 16px;
        }
      }
    }

    // 操作按钮样式
    .action-buttons {
      display: flex;
      justify-content: center;
      gap: 12px;
      padding-top: 20px;
    }
  }
</style>
