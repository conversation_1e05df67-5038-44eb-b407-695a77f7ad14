<script setup lang="ts">
  import { BasicModal, useModalInner } from '@hiip/internal/components/Modal';
  import { useMessage } from '@hiip/internal/hooks/web/useMessage';
  import { Upload } from 'ant-design-vue';
  import type { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface';
  import { useToggle } from '@vueuse/core';
  import { nextTick, ref } from 'vue';
  import { Button } from '@hiip/internal/components/Button';
  import { Icon } from '@hiip/internal/components/Icon';
  import { MarkdownRender } from '@hiip/internal/components/MarkdownRender';
  import type { ModalEditDT } from './data';
  import { importPdf } from '/@/api/metric';

  const { createConfirm } = useMessage();

  const emit = defineEmits(['register', 'sussess', 'minimize']);

  const [registerModal, { closeModal: closeModal }] = useModalInner<ModalEditDT>((_data) => {});

  async function handleOk() {
    emit('sussess');
    closeModal();
  }

  // 最小化处理函数
  function handleMinimize() {
    emit('minimize');
  }

  const [_loading, setLoading] = useToggle(false);

  // 流式处理状态变量
  const messages = ref<string>('');
  const lastProcessedLength = ref(0);
  const isUploading = ref(false);
  const abortController = ref<AbortController | null>(null);
  const messageHistoryRef = ref<HTMLElement | null>(null);

  // 解析单行数据
  function parseDataLine(line: string): string | null {
    if (!line.trim()) return null;

    // 解析 "data: {"content":"消息内容"}" 格式
    if (line.startsWith('data: ')) {
      try {
        const jsonStr = line.substring(6); // 移除 "data: " 前缀
        const jsonData = JSON.parse(jsonStr);
        return jsonData.content || null;
      } catch (e) {
        console.error('JSON解析错误:', e);
        return null;
      }
    }
    return null;
  }

  // 处理流式数据
  function processStreamData(chunk: string) {
    const newChunk = chunk.substring(lastProcessedLength.value);
    lastProcessedLength.value = chunk.length;

    // 按行分割数据
    const lines = newChunk.split('\n');
    for (const line of lines) {
      const content = parseDataLine(line);
      if (content) {
        messages.value += content;

        // 自动滚动到底部
        nextTick(() => {
          if (messageHistoryRef.value) {
            messageHistoryRef.value.scrollTop = messageHistoryRef.value.scrollHeight;
          }
        });
        emit('sussess');
      }
    }
  }

  // 取消上传功能
  function cancelUpload() {
    if (abortController.value) {
      abortController.value.abort();
      abortController.value = null;
    }
    isUploading.value = false;
    setLoading(false);
    messages.value += '用户已取消\n';
  }

  function customRequest(options: UploadRequestOption) {
    const formData = new FormData();
    formData.append('file', options.file);

    // 重置状态
    messages.value = '';
    lastProcessedLength.value = 0;
    isUploading.value = true;
    setLoading(true);

    // 创建AbortController用于取消请求
    abortController.value = new AbortController();

    importPdf(formData, {
      signal: abortController.value.signal,
      onDownloadProgress: (progressEvent) => {
        const chunk = progressEvent.event.target.response;
        if (chunk) {
          processStreamData(chunk);
        }
      },
    })
      .then(() => {
        messages.value += 'PDF上传和处理完成！\n';
      })
      .catch((error) => {
        if (error.name === 'AbortError') {
          messages.value += '用户已取消\n';
        } else {
          console.error('上传错误:', error);
        }
      })
      .finally(() => {
        isUploading.value = false;
        setLoading(false);
        abortController.value = null;
      });
  }

  async function handleClose() {
    if (!isUploading.value) return true;
    let isConfirm = false;
    createConfirm({
      title: '提示',
      iconType: 'warning',
      content: '关闭后将取消处理，您确定吗？',
      onOk: () => {
        isConfirm = true;
        cancelUpload();
      },
    });
    return isConfirm;
  }
</script>

<template>
  <BasicModal
    wrap-class-name="import-standard-file-modal"
    @register="registerModal"
    title="导入标准文件"
    :can-fullscreen="false"
    :destroy-on-close="false"
    :width="700"
    :draggable="false"
    :min-height="400"
    :use-wrapper="false"
    centered
    @ok="handleOk"
    :show-cancel-btn="false"
    :show-ok-btn="false"
    :closeFunc="handleClose"
  >
    <div class="flex flex-col gap-4 justify-center">
      <Upload
        v-if="!isUploading"
        class="index-pdf-upload ma"
        :accept="'.pdf'"
        :custom-request="customRequest"
        :disabled="isUploading"
        :show-upload-list="false"
      >
        <Button
          type="dashed"
          class="!w-45 !h-30 !flex flex-col items-center justify-center"
          :class="[
            !isUploading ? '!border-gray-300 !text-primary' : '!border-gray-300 !text-gray-300',
          ]"
        >
          <Icon :size="32" icon="ant-design:upload-outlined" />
          点击上传标准文件(PDF)
        </Button>
      </Upload>

      <!-- 消息显示区域 -->
      <div class="message-container" v-if="isUploading">
        <!-- 消息列表 -->
        <div
          ref="messageHistoryRef"
          class="message-history min-h-80 max-h-80 overflow-y-auto border rounded p-2 bg-gray-50"
        >
          <MarkdownRender :content="messages" />
        </div>
      </div>

      <div class="mb-2 flex justify-center gap-4" v-if="isUploading">
        <Button type="primary" ghost @click="handleMinimize"> 最小化 </Button>
        <Button type="primary" danger @click="cancelUpload"> 取消处理 </Button>
      </div>
    </div>
  </BasicModal>
</template>

<style lang="scss">
  .import-standard-file-modal {
    .ant-modal-header {
      background: transparent;
    }
    .ant-modal-content {
      background: linear-gradient(
        180deg,
        rgba(225, 237, 253, 1),
        rgba(255, 255, 255, 1) 20%,
        rgba(255, 255, 255, 1) 100%
      );
    }
  }
</style>
