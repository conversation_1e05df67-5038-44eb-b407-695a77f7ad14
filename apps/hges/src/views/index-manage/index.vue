<script setup lang="ts">
  import type { ActionItem } from '@hiip/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@hiip/internal/components/Table';
  import { Button } from '@hiip/internal/components/Button';
  import { useModal } from '@hiip/internal/components/Modal';
  import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
  import { Upload } from 'ant-design-vue';
  import { useClipboard, useToggle } from '@vueuse/core';
  import { exportUtil } from '@hiip/internal/utils';
  import { useAppHeader } from '@hiip/internal/utils/auth/useAppHeader';
  import { useMessage } from '@hiip/internal/hooks/web/useMessage';
  import { Icon } from '@hiip/internal/components/Icon';
  import { ref } from 'vue';
  import { columns, schemas } from './data';
  import EditModal from './EditModal.vue';
  import * as metricApi from '/@/api/metric';
  import PDFImportModal from './PDFImportModal.vue';
  import GenerateSqlModal from './GenerateSqlModal.vue';

  const [registerTable, tableFns] = useTable({
    api: metricApi.getPages,
    useSearchForm: true,
    formConfig: {
      labelWidth: 120,
      schemas,
      autoSubmitOnEnter: true,
    },
    columns,
    actionColumn: {
      dataIndex: 'action',
      title: '操作',
      width: 100,
      fixed: 'right',
    },
    inset: true,
    resizeHeightOffset: 20,
  });
  const [registerEditModal, { openModal }] = useModal();

  function onAdd() {
    openModal(true, {
      mode: 'add',
    });
  }

  function onEdit(record: metricApi.IMetric) {
    openModal(true, {
      mode: 'edit',
      record,
    });
  }

  function onDelete(record: metricApi.IMetric) {
    metricApi.remove(record.metricId).then(() => {
      tableFns.reload();
    });
  }

  function createActions(record: metricApi.IMetric) {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        type: 'link',
        onClick: onEdit.bind(null, record),
        ifShow: false,
      },
      {
        label: '删除',
        type: 'link',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          placement: 'topRight',
          confirm: onDelete.bind(null, record),
          okButtonProps: { danger: true },
        },
      },
    ];

    return actions;
  }

  const [downloading, setDownloading] = useToggle();
  function onDownload() {
    setDownloading(true);
    exportUtil(metricApi.downloadTemplate()).finally(() => {
      setDownloading(false);
    });
  }

  const { createMessage } = useMessage();
  const [uploadLoading, setUploadLoading] = useToggle(false);

  function onUploadChange({ file }: UploadChangeParam<UploadFile<any>>) {
    setUploadLoading(true);
    if (file.status === 'error' || file.status === 'done') {
      setUploadLoading(false);
      if (file.status === 'error' || file.response?.code !== '0') {
        createMessage.error(`上传失败,${file.response?.message}!`);
        return;
      }
      createMessage.success('上传成功!');
      tableFns.reload();
    }
  }
  const headers = useAppHeader();

  const [exportLoading, setExportLoading] = useToggle(false);
  const onExport = async () => {
    setExportLoading(true);
    await exportUtil(metricApi.exportData(tableFns.getForm().getFieldsValue()));
    setExportLoading(false);
  };

  const { copy } = useClipboard({
    legacy: true,
  });

  function onCopy(value: string) {
    copy(value).then(() => {
      createMessage.success('已复制到剪贴板!');
    });
  }

  const [registerPDFImportModal, { openModal: openPDFImportModal, setModalProps }] = useModal();
  const [
    registerGenerateSqlModal,
    { openModal: openGenerateSqlModal, setModalProps: setGenerateSqlModalProps },
  ] = useModal();

  // PDF导入最小化状态管理
  const isMinimized = ref(false);
  const isGenerateSqlMinimized = ref(false);

  function onPDFImport() {
    isMinimized.value = false; // 重置最小化状态
    openPDFImportModal(true);
  }

  // 最小化处理函数
  function onMinimize() {
    isMinimized.value = true;
    setModalProps({ visible: false });
  }

  // 恢复窗口处理函数
  function onRestore() {
    isMinimized.value = false;
    setModalProps({ visible: true });
  }

  function onGenerateSql() {
    isGenerateSqlMinimized.value = false; // 重置最小化状态
    openGenerateSqlModal(true, {});
  }

  // 最小化处理函数
  function onGenerateSqlMinimize() {
    isGenerateSqlMinimized.value = true;
    setGenerateSqlModalProps({ visible: false });
  }

  // 恢复窗口处理函数
  function onGenerateSqlRestore() {
    isGenerateSqlMinimized.value = false;
    setGenerateSqlModalProps({ visible: true });
  }
</script>

<template>
  <div class="p-4 bg-white h-[calc(100%-8px)] flex flex-col gap-4">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <div class="flex items-center gap-2">
          <Button type="primary" @click="onAdd" v-if="false">新增</Button>

          <Upload
            v-if="false"
            :headers="headers"
            :show-upload-list="false"
            :action="metricApi.importUrl"
            accept=".xls,.xlsx"
            @change="onUploadChange"
          >
            <Button type="primary" ghost :loading="uploadLoading">Excel导入</Button>
          </Upload>

          <Button v-if="false" type="primary" ghost :loading="downloading" @click="onDownload">
            下载模板
          </Button>
          <Button type="primary" :loading="downloading" @click="onPDFImport"> 导入标准文件 </Button>
          <Button type="primary" @click="onGenerateSql" pre-icon="ai-gen|svg"> AI自动生成 </Button>
        </div>
      </template>
      <template #toolbar>
        <Button @click="onExport" :loading="exportLoading">导出 Excel</Button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record)" />
        </template>
        <span
          class="relative w-full flex pr-4 min-w-0 group"
          v-else-if="Object.keys(record).includes(column.dataIndex)"
        >
          <span class="flex-1 basis-0 truncate" :title="record[column.dataIndex]">
            {{ record[column.dataIndex] }}
          </span>
          <span
            class="absolute right-0 top-0 !hidden !group-hover:block cursor-pointer text-primary"
            @click="onCopy(record[column.dataIndex])"
          >
            <Icon icon="ant-design:copy-outlined" />
          </span>
        </span>
      </template>
    </BasicTable>
    <EditModal @register="registerEditModal" @sussess="tableFns.reload" />
    <PDFImportModal
      @register="registerPDFImportModal"
      @sussess="tableFns.reload"
      @minimize="onMinimize"
    />
    <GenerateSqlModal
      @register="registerGenerateSqlModal"
      @sussess="tableFns.reload"
      @minimize="onGenerateSqlMinimize"
    />

    <!-- 悬浮恢复按钮 -->
    <div v-if="isMinimized" class="fixed top-4 right-4 z-50" @click="onRestore">
      <Button
        type="primary"
        class="!w-12 !h-12 !rounded-full !flex items-center justify-center shadow-lg"
      >
        <Icon icon="ant-design:arrow-up-outlined" :size="20" />
      </Button>
    </div>
    <div
      v-if="isGenerateSqlMinimized"
      class="fixed top-[50%] right-4 z-50"
      @click="onGenerateSqlRestore"
    >
      <Button
        type="primary"
        class="!w-12 !h-12 !rounded-full !flex items-center justify-center shadow-lg"
      >
        <Icon icon="ant-design:arrow-up-outlined" :size="20" />
      </Button>
    </div>
  </div>
</template>
