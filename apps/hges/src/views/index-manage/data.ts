import type { BasicColumn, FormSchema } from '@hiip/internal/components/Table';
import type { IMetric } from '/@/api/metric';

export interface ModalEditDT {
  mode: 'add' | 'edit';
  record?: IMetric;
}

export enum LogicWayEnum {
  SQL = 'SQL',
  /** 导表 */
  IMPORT_TABLE = '导表',
}

export const schemas: FormSchema[] = [
  {
    label: '序号',
    field: 'metricNo',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '指标代码',
    field: 'metricCode',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '责任人',
    field: 'metricPerson',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '章节',
    field: 'metricChapter',
    component: 'Input',
    colProps: { span: 6 },
  },
  // 标准版本
  {
    label: '标准版本',
    field: 'version',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '一级指标',
    field: 'metricOne',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '二级级指标',
    field: 'metricTwo',
    component: 'Input',
    colProps: { span: 6 },
  },
  // 取数逻辑方式
  {
    label: '取数逻辑方式',
    field: 'metricDataLogic',
    component: 'Select',
    componentProps: {
      options: [
        { label: 'SQL', value: LogicWayEnum.SQL },
        { label: '导表', value: LogicWayEnum.IMPORT_TABLE },
      ],
    },
    colProps: { span: 6 },
  },
];

/**
 * 序号	代码	系统数据来源	章节	一级指标	二级指标	三级指标	四级指标	分子&分母	计算公式	临床确认逻辑	取数逻辑方式	SQL语句	是否不适用	指标责任科室	责任人	是否可以修改	高优低优
 */
export const columns: BasicColumn[] = [
  // 标准版本
  {
    title: '标准版本',
    dataIndex: 'version',
  },
  {
    title: '章节',
    dataIndex: 'metricChapter',
  },
  {
    title: '一级指标',
    dataIndex: 'metricOne',
  },
  {
    title: '二级指标',
    dataIndex: 'metricTwo',
  },
  {
    title: '三级指标',
    dataIndex: 'metricThree',
  },
  {
    title: '四级指标',
    dataIndex: 'metricFour',
  },
  {
    title: '指标名称',
    dataIndex: 'metricName',
  },
  {
    title: '定义',
    dataIndex: 'metricDefine',
  },
  {
    title: '分子',
    dataIndex: 'metricNumerator',
  },
  {
    title: '分母',
    dataIndex: 'metricDenominator',
  },

  // {
  //   title: '分子&分母',
  //   dataIndex: 'metricType',
  // },
  // {
  //   title: '计算公式',
  //   dataIndex: 'metricFormula',
  // },
  // {
  //   title: '临床确认逻辑',
  //   dataIndex: 'clinicalConfirmLogic',
  // },
  // {
  //   title: '取数逻辑方式',
  //   dataIndex: 'metricDataLogic',
  // },
  // {
  //   title: 'SQL语句',
  //   dataIndex: 'metricSql',
  // },
  // {
  //   title: '是否不适用',
  //   dataIndex: 'applyState',
  //   customRender: ({ record }) => {
  //     // 是否不适用 0:否 1:是
  //     const applyStateMap = {
  //       1: '是',
  //       0: '否',
  //     };
  //     return applyStateMap[record.applyState];
  //   },
  // },
  // {
  //   title: '指标责任科室',
  //   dataIndex: 'metricDept',
  // },
  // {
  //   title: '责任人',
  //   dataIndex: 'metricPerson',
  // },
  // {
  //   title: '是否可以修改',
  //   dataIndex: 'modifiable',
  // },
  // {
  //   title: '高优低优',
  //   dataIndex: 'highAndLow',
  // },
  // {
  //   title: '修改人',
  //   dataIndex: 'updateUser',
  //   width: 100,
  // },
  // {
  //   title: '修改时间',
  //   dataIndex: 'updateTime',
  //   width: 180,
  // },
];

/**
 * 序号	代码	系统数据来源	章节	一级指标	二级指标	三级指标	四级指标	分子&分母	计算公式	临床确认逻辑	取数逻辑方式	SQL语句	是否不适用	指标责任科室	责任人	是否可以修改	高优低优
 */
export const EditSchemas: FormSchema[] = [
  {
    label: '指标ID',
    field: 'metricId',
    component: 'Input',
    show: false,
  },
  {
    label: '序号',
    field: 'metricNo',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
    required: true,
  },
  {
    label: '代码',
    field: 'metricCode',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
    required: true,
  },
  {
    label: '系统数据来源',
    field: 'metricDataSource',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '章节',
    field: 'metricChapter',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '一级指标',
    field: 'metricOne',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '二级指标',
    field: 'metricTwo',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '三级指标',
    field: 'metricThree',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '四级指标',
    field: 'metricFour',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '分子&分母',
    field: 'metricType',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '计算公式',
    field: 'metricFormula',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '临床确认逻辑',
    field: 'clinicalConfirmLogic',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '取数逻辑方式',
    field: 'metricDataLogic',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: 'SQL', value: LogicWayEnum.SQL },
        { label: '导表', value: LogicWayEnum.IMPORT_TABLE },
      ],
    },
    defaultValue: LogicWayEnum.SQL,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: 'SQL语句',
    field: 'metricSql',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
    ifShow: ({ values }) => values.metricDataLogic === LogicWayEnum.SQL,
  },
  {
    label: '指标责任科室',
    field: 'metricDept',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '责任人',
    field: 'metricPerson',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '是否可以修改',
    field: 'modifiable',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '是否适用',
    field: 'applyState',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
    defaultValue: 0,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    label: '高优低优',
    field: 'highAndLow',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
];
