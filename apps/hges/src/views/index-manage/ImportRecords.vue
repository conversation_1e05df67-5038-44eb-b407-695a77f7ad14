<script lang="ts" setup>
  import { BasicTable, useTable } from '@hiip/internal/components/Table';
  import { getImportRecord } from '/@/api/metric';

  const [registerTable] = useTable({
    api: getImportRecord,
    columns: [
      {
        title: '版本',
        dataIndex: 'version',
      },
      {
        title: '标准名称',
        dataIndex: 'standardName',
      },
      {
        title: '导入时间',
        dataIndex: 'createTime',
      },
      {
        title: '导入人',
        dataIndex: 'createUser',
      },
    ],
    pagination: false,
  });
</script>

<template>
  <div>
    <BasicTable @register="registerTable" />
  </div>
</template>
