import { ContentTypeEnum } from '@hiip/internal/enums/httpEnum';
import { useGlobSetting } from '@hiip/internal/hooks/setting';
import { defHttp } from '@hiip/internal/utils/http/axios';
import type { AxiosRequestConfig } from '@hiip/internal/utils/http/axios/Axios';

export interface IMetric {
  /** 是否不适用 0:否 1:是 */
  applyState: number;
  /** 临床确认逻辑 */
  clinicalConfirmLogic: string;
  /** 高优低优 */
  highAndLow: string;
  /** 章节 */
  metricChapter: string;
  /** 代码 */
  metricCode: string;
  /** 取数逻辑方式 */
  metricDataLogic: string;
  /** 系统数据来源 */
  metricDataSource: string;
  /** 指标责任科室 */
  metricDept: string;
  /** 计算公式 */
  metricFormula: string;
  /** 四级指标 */
  metricFour: string;
  /** 指标ID */
  metricId: string;
  /** 序号 */
  metricNo: string;
  /** 一级指标 */
  metricOne: string;
  /** 责任人 */
  metricPerson: string;
  /** SQL语句 */
  metricSql: string;
  /** 三级指标 */
  metricThree: string;
  /** 二级指标 */
  metricTwo: string;
  /** 分子&分母 */
  metricType: string;
  /** 是否可以修改 */
  modifiable: string;
}

export interface IMetricPage {
  /** 章节 */
  metricChapter: string;
  /** 指标代码 */
  metricCode: string;
  /** 指标名称 */
  metricName: string;
  /** 指标序号 */
  metricNo: string;
  /** 一级指标 */
  metricOne: string;
  /** 责任人 */
  metricPerson: string;
  /** 二级指标 */
  metricTwo: string;
}

/**
 * 指标管理-分页查询指标信息
 */
export function getPages(data: Partial<IMetricPage>) {
  return defHttp.post({ url: `/metric/page`, data });
}

/**
 * 指标管理-新增指标信息
 */
export function add(data: Partial<IMetric>) {
  return defHttp.post({ url: `/metric/add`, data });
}

/**
 * 指标管理-修改指标信息
 */
export function update(data: Partial<IMetric>) {
  return defHttp.post({ url: `/metric/update`, data });
}

/**
 * 指标管理-删除指标信息
 */
export function remove(id: string) {
  return defHttp.post({ url: `/metric/delete?id=${id}` });
}
/**
 * 指标管理-下载指标管理模板
 */
export function downloadTemplate() {
  return defHttp.post(
    { url: `/metric/downloadTemplate`, responseType: 'blob' },
    { isReturnNativeResponse: true },
  );
}

const { apiUrl, urlPrefix } = useGlobSetting();
export const importUrl = `${apiUrl}${urlPrefix}/metric/import`;

export function exportData(data: Partial<IMetric>) {
  return defHttp.post(
    { url: `/metric/export`, responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
}

/**
 * post /ai/api/files/upload
 */
export function importPdf(file: FormData, config: AxiosRequestConfig) {
  return defHttp.post(
    {
      url: `/ai/api/files/upload`,
      data: file,
      headers: { 'Content-Type': ContentTypeEnum.FORM_DATA },
      timeout: 0,
      ...config,
    },
    { isReturnNativeResponse: true },
  );
}

/**
 * post /ai/api/metric/metricToSql
 */
export function metricToSql(config: AxiosRequestConfig) {
  return defHttp.post(
    {
      url: `/ai/api/metric/metricToSql`,
      responseType: 'stream',
      timeout: 0,
      ...config,
    },
    { isReturnNativeResponse: true },
  );
}

/**
 * 获取省份列表
 * /ai/api/top3IndexImportRecord/provinceDict
 */

export function getProvinceDict() {
  return defHttp.get(
    { url: `/ai/api/top3IndexImportRecord/provinceDict` },
    { isReturnNativeResponse: true },
  );
}

export interface IImportRecord {
  version: string;
  standardName: string;
  createTime: string;
  createUser: string;
}
/**
 * 获取标准列表
 * /ai/api/top3IndexImportRecord/queryAll
 */
export function getImportRecord() {
  return defHttp.get<IImportRecord[]>(
    { url: `/ai/api/top3IndexImportRecord/queryAll` },
    { isReturnNativeResponse: true },
  );
}
