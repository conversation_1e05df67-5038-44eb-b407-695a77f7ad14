import type { BasicColumn, FormSchema } from '@hiip/internal/components/Table';
import { DBType, getDataSourceList } from '/@/api/data-source';
import {
  getDatabasesKVList,
  getETLFieldInfo,
  getEtlFieldInfoOfStep,
  getMaxOrMin,
  getTableInfo,
} from '/@/api/table-structure';
import { h } from 'vue';
import { getDictSelectItemByInfoCT } from '@hiip/internal/api';
import type { Rule } from '@hiip/internal/components/Form';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import dayjs from 'dayjs';
dayjs.extend(customParseFormat);
export enum TaskTypeEnum {
  /** 轮询任务 */
  Polling,
  /** 一次性任务 */
  OneTime,
  /** 增量 */
  Increment,
}

export const TaskTypeTextMap = {
  [TaskTypeEnum.OneTime]: '一次性任务',
  [TaskTypeEnum.Polling]: '轮询任务',
  [TaskTypeEnum.Increment]: '增量任务',
};

export const TaskTypeMenus = [
  {
    label: '一次性任务',
    value: TaskTypeEnum.OneTime,
  },
  {
    label: '轮询任务',
    value: TaskTypeEnum.Polling,
  },
  {
    label: '增量任务',
    value: TaskTypeEnum.Increment,
  },
];

export const TaskTypeTagMap = {
  [TaskTypeEnum.Polling]: '轮询',
  [TaskTypeEnum.OneTime]: '一次性',
  [TaskTypeEnum.Increment]: '增量',
};

// 	增量列类型 1-Datetime 2-字符串 3-Timestamp 4数字
export enum IncrementColumnTypeEnum {
  Datetime = 1,
  String = 2,
  Timestamp = 3,
  Number = 4,
}

export const IncrementColumnTypeInitValue = {
  [IncrementColumnTypeEnum.Datetime]: '1900-01-01 00:00:00',
  [IncrementColumnTypeEnum.String]: '-1',
  [IncrementColumnTypeEnum.Timestamp]: '1900-01-01 00:00:00.000',
  [IncrementColumnTypeEnum.Number]: 0,
};

export enum KeepRunningEnum {
  NO,
  YES,
}

export const KeepRunningTextMap = {
  [KeepRunningEnum.NO]: '否',
  [KeepRunningEnum.YES]: '是',
};

export enum TaskStatusEnum {
  Stop,
  Running,
  Error,
}

export const TaskStatusTextMap = {
  [TaskStatusEnum.Stop]: {
    text: '停止',
    color: 'rgba(240, 176, 0, 0.7)',
  },
  [TaskStatusEnum.Running]: {
    text: '运行',
    color: '#4DC771',
  },
  [TaskStatusEnum.Error]: {
    text: '异常',
    color: '#F15226',
  },
};

export enum BatchOperationEnum {
  /**
   * 启动
   */
  Start,
  /**
   * 停止
   */
  Stop,
  /**
   * 删除
   */
  Delete,
}

export const SQLFormatLanguageMap = {
  [DBType.Oracle]: 'plsql',
  [DBType.MySQL]: 'mysql',
  [DBType.SQLServer]: 'tsql',
  [DBType.PostgreSQL]: 'postgresql',
};

export const schemas: FormSchema[] = [
  {
    field: 'medicalInstitutionName',
    label: '医疗机构',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictSelectItemByInfoCT({ dictCode: 'HOSPITAL_CODE' }),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'taskCode',
    label: '任务Code',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'taskName',
    label: '任务名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'taskType',
    label: '任务类型',
    component: 'Select',
    componentProps: {
      options: Object.entries(TaskTypeTextMap).map(([key, value]) => ({
        label: value,
        value: key,
      })),
    },
    colProps: { span: 6 },
  },

  {
    field: 'sourceDatasourceId',
    label: '源数据源',
    component: 'ApiSelect',
    componentProps: {
      api: getDataSourceList,
      labelField: 'dataSourceName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'sourceTableName',
    label: '源表名',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'targetDatasourceId',
    label: '目标数据源',
    component: 'ApiSelect',
    componentProps: {
      api: getDataSourceList,
      labelField: 'dataSourceName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'targetTableName',
    label: '目标表名',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'status',
    label: '运行状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '运行', value: TaskStatusEnum.Running },
        { label: '停止', value: TaskStatusEnum.Stop },
        { label: '异常', value: TaskStatusEnum.Error },
      ],
    },
    colProps: { span: 6 },
  },
  {
    field: 'ip',
    label: 'IP地址',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'port',
    label: '端口',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'RangePicker',
    componentProps: {
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
];

export const columns: BasicColumn[] = [
  {
    title: '任务Code',
    dataIndex: 'taskCode',
    align: 'left',
    width: 240,
  },
  {
    title: '任务名称',
    dataIndex: 'taskName',
    align: 'left',
    width: 220,
  },
  {
    title: '任务类型',
    dataIndex: 'taskType',
    align: 'center',
    width: 120,
    customRender({ text }) {
      const bgColors = {
        [TaskTypeEnum.Polling]: 'bg-primary',
        [TaskTypeEnum.OneTime]: 'bg-yellow-text',
        [TaskTypeEnum.Increment]: 'bg-cyan-text',
      };
      return h(
        'span',
        {
          class: `${bgColors[text]} text-white rounded p-1 bg-op-80`,
        },
        TaskTypeTagMap[text],
      );
    },
  },
  {
    title: '轮询/s',
    dataIndex: 'pollingSeconds',
    width: 80,
    align: 'center',
    ellipsis: true,
  },
  {
    title: '运行状态',
    dataIndex: 'status',
    align: 'center',
    width: 90,
    customRender({ text }) {
      return h(
        'span',
        {
          class: `text-white rounded p-1 bg-op-70`,
          style: {
            backgroundColor: TaskStatusTextMap[text].color,
          },
        },
        TaskStatusTextMap[text].text,
      );
    },
  },
  {
    dataIndex: 'keepRunning',
    align: 'center',
    width: 140,
    customRender({ text }) {
      return h(
        'span',
        {
          class: `text-white rounded p-1 bg-op-70 ${
            text === KeepRunningEnum.YES ? 'bg-green-text' : 'bg-yellow-text'
          }`,
        },
        KeepRunningTextMap[text],
      );
    },
  },
  {
    title: '运行日志',
    dataIndex: 'logCount',
    width: 90,
    align: 'center',
  },
  {
    title: '增量列记录值',
    dataIndex: 'incrementValue',
    align: 'center',
    width: 150,
  },
  {
    title: 'IP地址',
    dataIndex: 'ip',
    align: 'center',
    width: 140,
  },
  {
    title: '端口',
    dataIndex: 'port',
    width: 80,
  },
  {
    title: '源数据源',
    dataIndex: 'sourceDatasourceName',
    align: 'center',
    width: 120,
  },
  {
    title: '源表名',
    dataIndex: 'sourceTableName',
    align: 'center',
    width: 150,
  },
  {
    title: '目标数据源',
    dataIndex: 'targetDatasourceName',
    align: 'center',
    width: 120,
  },
  {
    title: '目标表名',
    dataIndex: 'targetTableName',
    align: 'center',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'center',
    width: 180,
  },
];

export enum ETLEditFieldEnum {
  Sql = 'sql',
}

export const incrementValueRules: FormSchema['dynamicRules'] = ({ model }) => {
  const rules: Rule[] = [{ required: true, message: '请输入' }];
  if (model.incrementColumnType === IncrementColumnTypeEnum.Datetime) {
    let label = 'Datetime';
    if (model.taskType === TaskTypeEnum.Increment) {
      label = 'Date';
    }
    rules.push({
      validator(_, value) {
        if (!value) {
          return Promise.reject(`请输入符合${label}类型的值`);
        }
        if (model.taskType === TaskTypeEnum.Increment && value.length !== 10) {
          return Promise.reject(`请输入符合${label}类型的值`);
        }
        if (
          model.taskType !== TaskTypeEnum.Increment &&
          value.length !== '1900-01-01 00:00:00'.length
        ) {
          return Promise.reject(`请输入符合${label}类型的值`);
        }
        if (/^\d{4}/.test(value) && dayjs(value.substr(5, 10), 'MM-DD').isValid()) {
          return Promise.resolve();
        } else {
          return Promise.reject(`请输入符合${label}类型的值`);
        }
      },
    });
  }
  if (model.incrementColumnType === IncrementColumnTypeEnum.Number) {
    rules.push({ pattern: /^-?\d+$/, message: '请输入符合数字类型的值' });
  }

  return rules;
};

function getSchemaName(opt) {
  switch (opt.dbType) {
    case DBType.Oracle:
    case DBType.DM:
      return opt.userName;
    case DBType.SQLServer:
      return 'dbo';
    default:
      return opt.initialDatabaseName;
  }
}

export const EditSchemas: FormSchema[] = [
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'taskType',
    label: '任务类型',
    component: 'InputNumber',
    show: false,
  },
  {
    field: 'moduleName0',
    label: '任务信息',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'taskCode',
    label: '任务Code',
    component: 'Input',
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'taskName',
    label: '任务名称',
    component: 'Input',
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'incrementColumnMaxValue',
    label: '增量列记录值',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    colProps: { span: 12 },
    ifShow: ({ model }) => [TaskTypeEnum.Polling, TaskTypeEnum.Increment].includes(model.taskType),
  },

  {
    field: 'pollingSeconds',
    label: '轮询/s',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
    colProps: { span: 12 },
    required: true,
    ifShow: ({ model }) => [TaskTypeEnum.Polling, TaskTypeEnum.Increment].includes(model.taskType),
  },
  {
    field: 'keepRunning',
    label: '持续运行',
    component: 'RadioGroup',
    componentProps: {
      //0否 1是 默认0
      options: [
        { label: '否', value: KeepRunningEnum.NO },
        { label: '是', value: KeepRunningEnum.YES },
      ],
    },
    dynamicDisabled: ({ model }) => model.taskType === TaskTypeEnum.OneTime,
    colProps: { span: 12 },
    defaultValue: 0,
    helpMessage: '停止状态下会被定时任务自动拉起运行',
  },
  {
    field: 'restartCount',
    label: '重启次数',
    component: 'InputNumber',
    colProps: { span: 12 },
    defaultValue: 5,
    ifShow: ({ model }) => model.keepRunning === KeepRunningEnum.YES,
    helpMessage: '达到最大重启次数后不再自动拉起任务',
  },
  {
    field: 'moduleName1',
    label: '源数据源信息',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'sourceDatabaseName',
    label: '源数据库名',
    component: 'Input',
    show: false,
  },
  {
    field: 'sourceDatasourceId',
    label: '源数据源',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getDataSourceList,
        labelField: 'dataSourceName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        onChange: (_, opt) => {
          if (opt) {
            formModel.sourceDatabaseName = opt?.label;
            formModel.sourceSchemaName = getSchemaName(opt);
          }
          formModel.sourceTableName = undefined;
        },
      };
    },
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'sourceSchemaName',
    label: '源表归属模式',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => {
          if (!formModel.sourceDatasourceId) {
            return Promise.resolve([]);
          }
          return getDatabasesKVList(formModel.sourceDatasourceId);
        },
        showSearch: true,
        optionFilterProp: 'label',
        onChange: () => {
          formModel.sourceTableName = undefined;
        },
      };
    },
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'sourceTableType',
    label: '源表类别',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '表', value: 1 },
        { label: '视图', value: 2 },
      ],
    },
    colProps: { span: 12 },
    defaultValue: 1,
  },
  {
    field: 'sourceTableName',
    label: '源表名',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: async () => {
          if (
            !formModel.sourceDatasourceId ||
            !formModel.sourceSchemaName ||
            !formModel.sourceTableType
          ) {
            return Promise.resolve([]);
          }
          const tableList = await getTableInfo({
            dataSourceId: formModel.sourceDatasourceId,
            databaseName: formModel.sourceSchemaName,
            tableType: formModel.sourceTableType,
          });
          return tableList.map((i) => ({
            ...i,
            tableDesc: `${i.tableName}${i.tableDesc ? '(' + i.tableDesc + ')' : ''}`,
          }));
        },
        labelField: 'tableDesc',
        valueField: 'tableName',
        optionFilterProp: 'label',
        showSearch: true,
      };
    },
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'incrementColumnName',
    label: '增量列名',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => {
          if (
            !formModel.sourceDatasourceId ||
            !formModel.sourceSchemaName ||
            !formModel.sourceTableName ||
            !formModel.sourceTableType
          ) {
            return Promise.resolve([]);
          }
          const fns = {
            [TaskTypeEnum.Polling]: getETLFieldInfo,
            [TaskTypeEnum.Increment]: getEtlFieldInfoOfStep,
          };
          const getIncrementColumns = fns[formModel.taskType];
          return getIncrementColumns({
            dataSourceId: formModel.sourceDatasourceId,
            databaseName: formModel.sourceSchemaName,
            tableName: formModel.sourceTableName,
            tableType: formModel.sourceTableType,
          });
        },
        labelField: 'columnName',
        valueField: 'columnName',
        showSearch: true,
        optionFilterProp: 'label',
        onChange: (_, opt) => {
          formModel.incrementColumnType = opt?.etlDataType;
        },
      };
    },
    colProps: { span: 12 },
    required: true,
    ifShow: ({ model }) => [TaskTypeEnum.Polling, TaskTypeEnum.Increment].includes(model.taskType),
  },
  {
    field: 'incrementColumnType',
    label: '增量列类型',
    component: 'RadioGroup',
    componentProps: ({ formModel }) => {
      const options = [
        // 	增量列类型 1-Datetime 2-字符串 3-Timestamp 4数字
        {
          label: formModel.taskType === TaskTypeEnum.Polling ? 'Datetime' : 'Date',
          value: IncrementColumnTypeEnum.Datetime,
        },
        ...(formModel.taskType === TaskTypeEnum.Polling
          ? [
              { label: '字符串', value: IncrementColumnTypeEnum.String },
              { label: 'Timestamp', value: IncrementColumnTypeEnum.Timestamp },
            ]
          : []),
        { label: '数字', value: IncrementColumnTypeEnum.Number },
      ];

      return {
        options,
        disabled: true,
        onChange: (e) => {
          if (formModel.taskType === TaskTypeEnum.Polling) {
            formModel.incrementColumnInitValue = IncrementColumnTypeInitValue[e?.target?.value];
          }
        },
      };
    },
    colProps: { span: 12 },
    ifShow: ({ model }) => [TaskTypeEnum.Polling, TaskTypeEnum.Increment].includes(model.taskType),
  },
  {
    field: 'incrementColumnInitValue',
    label: '增量列初始值',
    component: 'Input',
    componentProps: ({ formModel }) => {
      // 编辑时，不获取初始值
      if (formModel.id) {
        return;
      }
      if (formModel.taskType === TaskTypeEnum.Increment) {
        if (
          !formModel.sourceDatasourceId ||
          !formModel.sourceSchemaName ||
          !formModel.sourceTableName ||
          !formModel.incrementColumnName ||
          !formModel.incrementColumnType
        ) {
          return;
        }
        getMaxOrMin({
          dataSourceId: formModel.sourceDatasourceId,
          databaseName: formModel.sourceSchemaName,
          tableName: formModel.sourceTableName,
          fieldName: formModel.incrementColumnName,
          conversionToDateFlag:
            formModel.incrementColumnType === IncrementColumnTypeEnum.Datetime ? 1 : 0,
          dateTimeType: formModel.incrementColumnType === IncrementColumnTypeEnum.Datetime ? 1 : 2,
          maxOrMinFlag: 2,
        }).then((data) => {
          formModel.incrementColumnInitValue = data;
        });
      }

      if (formModel.taskType === TaskTypeEnum.Polling && formModel.incrementColumnType) {
        formModel.incrementColumnInitValue =
          IncrementColumnTypeInitValue[formModel?.incrementColumnType];
      }
      return {};
    },
    colProps: { span: 12 },
    dynamicRules: incrementValueRules,
    ifShow: ({ model }) => [TaskTypeEnum.Polling, TaskTypeEnum.Increment].includes(model.taskType),
  },
  {
    field: 'step',
    label: '增量步长',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
    colProps: { span: 7 },
    suffix({ values }) {
      const suffixMap = {
        [IncrementColumnTypeEnum.Datetime]: '天',
        [IncrementColumnTypeEnum.Number]: undefined,
      };
      return suffixMap[values.incrementColumnType];
    },
    required: true,
    ifShow: ({ model }) => [TaskTypeEnum.Increment].includes(model.taskType),
  },
  {
    field: 'redundancyPro',
    label: '冗余占比',
    component: 'InputNumber',
    labelWidth: 70,
    componentProps: {
      min: 0,
    },
    suffix: '%',
    colProps: { span: 5 },
    defaultValue: 5,
    required: true,
    ifShow: ({ model }) => [TaskTypeEnum.Increment].includes(model.taskType),
  },
  {
    field: 'batchCount',
    label: '批量数',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
    required: true,
    defaultValue: 5000,
    colProps: { span: 12 },
    ifShow: ({ model }) => [TaskTypeEnum.Polling, TaskTypeEnum.OneTime].includes(model.taskType),
  },
  {
    field: 'moduleName2',
    label: '目标数据源信息',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'targetDatasourceName',
    label: '目标数据源',
    component: 'Input',
    show: false,
  },
  {
    field: 'targetDatasourceId',
    label: '目标数据源',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getDataSourceList,
        labelField: 'dataSourceName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        onChange: (_, opt) => {
          formModel.targetDatasourceName = opt?.label;
          formModel.targetSchemaName = getSchemaName(opt);
          formModel.targetTableName = undefined;
        },
      };
    },
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'targetSchemaName',
    label: '目标表归属模式',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => {
          if (!formModel.targetDatasourceId) {
            return Promise.resolve([]);
          }
          return getDatabasesKVList(formModel.targetDatasourceId);
        },
        showSearch: true,
        optionFilterProp: 'label',
        onChange: () => {
          formModel.targetTableName = undefined;
        },
      };
    },
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'targetTableName',
    label: '目标表名',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => {
          if (!formModel.targetDatasourceId || !formModel.targetSchemaName) {
            return Promise.resolve([]);
          }
          return getTableInfo({
            dataSourceId: formModel.targetDatasourceId,
            databaseName: formModel.targetSchemaName,
            tableType: 1,
          });
        },
        labelField: 'tableName',
        valueField: 'tableName',
        optionFilterProp: 'label',
        showSearch: true,
      };
    },
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'targetTablePrimaryKeys',
    label: '目标表唯一标识',
    component: 'Input',
    componentProps: {
      placeholder: '请输入,多个用英文逗号隔开',
    },
    // component: 'ApiSelect',
    // componentProps: ({ formModel }) => {
    //   return {
    //     api: () => {
    //       if (
    //         !formModel.targetDatasourceId ||
    //         !formModel.targetSchemaName ||
    //         !formModel.targetTableName
    //       ) {
    //         return Promise.resolve([]);
    //       }
    //       return getColumnInfo({
    //         targetDatasourceId: formModel.targetDatasourceId,
    //         targetSchemaName: formModel.targetSchemaName,
    //         targetTableName: formModel.targetTableName,
    //       });
    //     },
    //     labelField: 'columnName',
    //     valueField: 'columnName',
    //     mode: 'multiple',
    //     maxTagCount: 1,
    //     placeholder: '请输入,多个用英文逗号隔开',
    //   };
    // },

    colProps: { span: 12 },
    required: true,
  },
  {
    field: ETLEditFieldEnum.Sql,
    label: 'SQL',
    component: 'SqlEditor',
    componentProps: {
      height: 260,
    },
    colProps: { span: 24 },
    dynamicRules: ({ model }) => {
      return [
        {
          required: model.sql,
          message: '请输入SQL',
        },
      ];
    },
  },
  {
    field: 'sourceIsOdsFlag',
    label: '是否为ODS生成',
    component: 'InputNumber',
    show: false,
  },
  {
    field: 'sql-action',
    label: ' ',
    component: 'Input',
    slot: 'sql-action',
  },
];
