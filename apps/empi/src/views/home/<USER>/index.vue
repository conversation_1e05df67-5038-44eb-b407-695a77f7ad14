<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts"></script>
<script lang="ts" setup>
  import type { Ref } from 'vue';
  import { onMounted, ref, watchEffect } from 'vue';
  import { useECharts } from '@hiip/internal/hooks/web/useECharts';
  import { basicProps } from './props';
  const emit = defineEmits(['import-info']);

  const props = defineProps({
    ...basicProps,
  });
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);
  const colorList = ['rgba(55,162,218,0.09)', 'rgba(103,224,227,0.09)', 'rgba(253,219,92,0.09)'];
  const _colorList = ['rgba(55,162,218,0.00)', 'rgba(103,224,227,0.00)', 'rgba(253,219,92,0.00)'];
  onMounted(() => {
    const myChart = getInstance();
    myChart?.on('click', function (params) {
      emit('import-info', params.dataIndex);
    });
  });

  watchEffect(() => {
    setOptions({
      title: {
        text: '索引处理统计',
        subtext: '',
        // top: -10,

        textStyle: {
          color: '#333333',
          fontSize: 14,
          fontWeight: 500,
        },
      },
      color: colorList,
      // tooltip: {
      //   trigger: 'axis',
      //   triggerOn: 'mousemove',
      //   axisPointer: {
      //     type: 'shadow',
      //     lineStyle: {
      //       color: 'rgba(0, 200, 255, 0.25)',
      //     },
      //   },
      // },
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        type: 'scroll',
        width: '80%',
        top: 4,
        right: 120,
        scrollDataIndex: 0,
        data: props?.linChartData?.map((item) => item.name),
      },
      xAxis: {
        type: 'category',
        data: props?.linChartData[0]?.dataX || [],
        splitLine: {
          show: true,
          lineStyle: {
            width: 1,
            type: 'solid',
            color: '#e0e6f1',
          },
        },
        axisTick: {
          show: false,
        },
      },

      yAxis: [
        {
          type: 'value',
          triggerEvent: true,
          // max: 80000,
          // splitNumber: 10,
          minInterval: 1,
          axisTick: {
            show: false,
          },
          axisLabel: {
            // interval: 1,
            color: '#999',
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: '#e0e6f1',
            },
          },
        },
      ],
      grid: {
        left: '1%',
        right: '1%',
        top: '10%',
        bottom: '1%',
        containLabel: true,
      },

      series: props?.linChartData?.map((item, index) => {
        let info = {
          name: item.name,
          smooth: true,
          data: item.dataY || [],
          type: 'line',
          //虚线
          lineStyle: {
            width: 4, // 线宽
            // type: 'dashed',
            // color: '#00C8FF',
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: colorList[index],
                },
                {
                  offset: 1,
                  color: _colorList[index],
                },
              ],
              global: false,
            },
          },
          symbol: 'circle', // 设置曲线形状为圆形
          itemStyle: {
            // color: '#00C8FF',
            borderColor: '#fff',
            borderWidth: 2,
          },
          symbolSize: 10,
        };
        return info;
      }),
    });
  });
</script>
