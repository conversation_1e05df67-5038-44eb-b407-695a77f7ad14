<template>
  <div ref="chartRef" class="w-full h-full"></div>
</template>
<script setup lang="ts">
  import { nextTick, ref, watchEffect } from 'vue';
  import type { Ref } from 'vue';
  import { useECharts } from '/@@/hooks/web/useECharts';
  import type { EChartsOption } from '/@@/hooks/web/useECharts';
  import { cloneDeep, map, set } from 'lodash-es';
  // 接收props
  const props = defineProps<{
    chartOption: EChartsOption;
    loading?: boolean;
  }>();

  const loadingConfig = {
    text: '加载中',
    color: '#1890ff',
    maskColor: 'rgba(255, 255, 255, 0.1)',
    textColor: '#1890ff',
    fontSize: 14,
    lineWidth: 3,
  };

  // 图表引用
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 更新图表
  const updateChart = () => {
    setOptions(props.chartOption);
    nextTick(() => {
      window.dispatchEvent(new Event('resize'));
    });
  };

  function cleanData(item: any) {
    set(item, 'data', []);
  }

  watchEffect(() => {
    updateChart();
  });

  watchEffect(async () => {
    if (!getInstance()) return;

    if (props.loading) {
      const emptyOption = cloneDeep(props.chartOption) as EChartsOption;
      Array.isArray(emptyOption.series)
        ? map(emptyOption.series, cleanData)
        : cleanData(emptyOption.series);
      Array.isArray(emptyOption.xAxis)
        ? map(emptyOption.xAxis, cleanData)
        : cleanData(emptyOption.xAxis);
      Array.isArray(emptyOption.legend)
        ? map(emptyOption.legend, cleanData)
        : cleanData(emptyOption.legend);

      setOptions(emptyOption).then(() => {
        if (props.loading) {
          getInstance()?.showLoading(loadingConfig);
        }
      });
    } else {
      getInstance()?.hideLoading();
      setOptions(props.chartOption);
    }
  });
</script>
