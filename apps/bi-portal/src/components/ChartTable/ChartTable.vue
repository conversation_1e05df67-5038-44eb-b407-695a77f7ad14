<template>
  <div class="bg-white rounded-md transition-shadow duration-300 flex flex-col gap-2 h-full">
    <div class="flex justify-between items-center">
      <Icon :size="26" icon="prefix|svg" />
      <span class="text-base font-medium text-gray-700 flex-1 of-hidden truncate" :title="title">
        {{ title }}
      </span>
      <div class="flex items-center gap-2">
        <slot name="extraBefore"></slot>
        <Button
          v-if="showDownload"
          pre-icon="ant-design:download-outlined"
          type="link"
          size="small"
          @click="handleDownload"
          :loading="downloadLoading"
        >
          下载
        </Button>
        <ModeSwitch v-if="showTab" v-model="chartType" />
        <slot name="extraAfter"></slot>
      </div>
    </div>
    <Tabs
      class="tab-switch"
      v-if="options.length > 1 && showTab"
      v-model:activeKey="activeTab"
      size="small"
    >
      <Tabs.TabPane v-for="option in options" :key="option.tab" :tab="option.tab" />
    </Tabs>
    <div class="flex-1 of-hidden">
      <Chart v-if="chartType === 'chart'" :chart-option="getChartOption" :loading="loading" />
      <Table v-else :data="getData" :columns="getColumns" :loading="loading" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';
  import { Button } from '@hiip/internal/components/Button';
  import { Icon } from '@hiip/internal/components/Icon';
  import { Tabs } from 'ant-design-vue';
  import type { ModeSwitchType } from '/@/components/ModeSwitch';
  import { ModeSwitch } from '/@/components/ModeSwitch';
  import Chart from './Chart.vue';
  import Table from './Table.vue';
  import type { ChartTableProps } from './types';

  // 接收props
  const props = withDefaults(defineProps<ChartTableProps>(), {
    options: () => [] as ChartTableProps['options'],
    loading: false,
    downloadLoading: false,
    showTab: true,
    showDownload: true,
    defaultChartType: 'chart',
  });

  const emit = defineEmits<{
    (e: 'download', tab?: string): void;
  }>();

  const chartType = ref<ModeSwitchType>(props.defaultChartType);
  const activeTab = ref<string>(props.options?.[0].tab || '');

  watch(
    () => props.options,
    (newOptions) => {
      activeTab.value = newOptions?.[0].tab || '';
    },
    { deep: true },
  );
  const getData = computed(() => {
    return props.options.find((option) => option.tab === activeTab.value)?.data || [];
  });
  const getColumns = computed(() => {
    return props.options.find((option) => option.tab === activeTab.value)?.columns || [];
  });
  const getChartOption = computed(() => {
    return props.options.find((option) => option.tab === activeTab.value)?.chartOption || {};
  });

  function handleDownload() {
    emit('download', activeTab.value);
  }
</script>
<style lang="scss" scoped>
  .tab-switch {
    :deep {
      &.ant-tabs-small > .ant-tabs-nav .ant-tabs-tab {
        padding: 4px 0;
      }
      .ant-tabs-nav {
        margin-bottom: 0;
      }
      .ant-tabs-tab + .ant-tabs-tab {
        margin-left: 8px;
      }

      &.ant-tabs-top > .ant-tabs-nav::before {
        border-bottom: none;
      }
    }
  }
</style>
