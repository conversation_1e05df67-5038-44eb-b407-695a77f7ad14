import type { EChartsOption } from '/@@/hooks/web/useECharts';
import type { ChartConfig } from './types';
import {
  getAreaStyle,
  getGrid,
  getLegend,
  getSeries,
  getTooltip,
  getXAxis,
  getYAxis,
} from './ChartUtils';
import { deepMerge } from '@hiip/internal/utils';
import { isNil } from 'lodash-es';

// 获取默认动画配置
export function getDefaultAnimationConfig() {
  return {
    animationEasing: 'cubicOut' as const,
    animationDuration: 800,
  };
}

// 处理图表数据的辅助方法
export function processChartData(data: Recordable[], field: string) {
  return data?.length ? data.map((item) => item[field]) : [];
}

// 处理系列数据
export function processSeriesData(
  data: Recordable[],
  yAxisField: any[],
  customProcessor?: (y: any, idx: number) => any,
) {
  if (!data?.length) return [];

  return yAxisField.map((y, idx) => {
    const baseConfig = {
      name: y.name,
      data: data.map((item) => item[y.field]),
    };

    return customProcessor ? customProcessor(y, idx) : baseConfig;
  });
}

/**
 * 通用折线图配置
 * @param opt 基础图表配置
 * @param options ECharts原生配置选项，用于覆盖或扩展默认配置
 */
export function createLineChart(opt: ChartConfig, options?: Partial<EChartsOption>): EChartsOption {
  const { data, xAxisField, yAxisField } = opt;

  // 生成默认配置
  const defaultConfig: EChartsOption = {
    tooltip: getTooltip({
      axisPointer: {
        type: 'cross',
      },
    }),
    grid: getGrid(),
    legend: getLegend({
      data: yAxisField.map((item: any) => item.name),
    }),
    xAxis: getXAxis({ data: processChartData(data, xAxisField) }),
    yAxis: getYAxis(),
    series: getSeries(
      processSeriesData(data, yAxisField, (y: any, idx: number) => ({
        name: y.name,
        data: data.map((item: any) => item[y.field]),
        type: 'line',
        ...(idx === 0
          ? {
              areaStyle: getAreaStyle(idx),
            }
          : {}),
      })),
    ),
    ...getDefaultAnimationConfig(),
  };

  // 如果传入了options，则进行深度合并
  if (options) {
    return deepMerge(defaultConfig, options);
  }

  return defaultConfig;
}

/**
 * 通用柱线混合图配置
 * @param opt 基础图表配置
 * @param options ECharts原生配置选项，用于覆盖或扩展默认配置
 */
export function createBarLineMixChart(
  opt: ChartConfig,
  options?: Partial<EChartsOption>,
): EChartsOption {
  const { data, xAxisField, yAxisField } = opt;

  // 生成默认配置
  const defaultConfig: EChartsOption = {
    tooltip: getTooltip({
      axisPointer: {
        type: 'shadow',
      },
    }),
    grid: getGrid(),
    legend: getLegend({ data: ['本期值', '同期值', '增长率'] }),
    xAxis: getXAxis({
      data: processChartData(data, xAxisField),
      axisLabel: {
        rotate: 45,
      },
    }),
    yAxis: getYAxis([
      {},
      {
        axisLabel: {
          formatter: '{value}%',
        },
        splitLine: {
          show: false,
        },
      },
    ]),
    series: getSeries(
      processSeriesData(data, yAxisField, (y: any, idx: number) => ({
        name: y.name,
        data: data.map((item: any) => item[y.field]),
        type: idx === yAxisField.length - 1 ? 'line' : 'bar',
        yAxisIndex: idx === yAxisField.length - 1 ? 1 : 0,
      })),
    ),
    ...getDefaultAnimationConfig(),
  };

  // 如果传入了options，则进行深度合并
  if (options) {
    return deepMerge(defaultConfig, options);
  }

  return defaultConfig;
}

/**
 * 通用柱状图配置（用于排名和类型分析）
 * @param opt 基础图表配置
 * @param options ECharts原生配置选项，用于覆盖或扩展默认配置
 */
export function createBarChart(opt: ChartConfig, options?: Partial<EChartsOption>): EChartsOption {
  const { data, xAxisField, yAxisField } = opt;

  // 生成默认配置
  const defaultConfig: EChartsOption = {
    tooltip: getTooltip({
      axisPointer: {
        type: 'shadow',
      },
    }),
    grid: getGrid({
      top: '10%',
      left: '8%',
      right: '8%',
      bottom: '10%',
    }),
    xAxis: getXAxis({ data: processChartData(data, xAxisField) }),
    yAxis: getYAxis(),
    series: [
      {
        name: '本期值',
        data: processChartData(data, yAxisField[0].field),
        type: 'bar',
        barWidth: 12,
        itemStyle: {
          color: '#56A1F5',
        },
      },
    ],
    ...getDefaultAnimationConfig(),
  };

  // 如果传入了options，则进行深度合并
  if (options) {
    return deepMerge(defaultConfig, options);
  }

  return defaultConfig;
}

/**
 * 雷达图配置
 * @param opt 基础图表配置
 * @param options ECharts原生配置选项，用于覆盖或扩展默认配置
 */
export function createRadarChart(
  opt: {
    data: any[];
    yAxisField: any[];
    xAxisField: any[];
  },
  options?: Partial<EChartsOption>,
): EChartsOption {
  const { data, yAxisField, xAxisField } = opt;

  // 生成默认配置
  const defaultConfig: EChartsOption = {
    legend: {
      data: yAxisField.map((item: any) => item.name),
      orient: 'vertical',
      right: 10,
      top: 60,
      bottom: 20,
      icon: 'circle',
    },
    radar: {
      indicator: xAxisField.map((item: any) => ({
        name: item.name,
      })),
      axisName: {
        color: '#333',
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#E0E6F1',
        },
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#E0E6F1',
        },
      },
      splitArea: {
        show: false,
      },
    },
    series: [
      {
        type: 'radar',
        data: data?.length
          ? yAxisField.map((y: any, idx: number) => ({
              name: yAxisField[idx]?.name || '',
              value: data.map((item: any) => (isNil(item[y.field]) ? 0 : Number(item[y.field]))),
            }))
          : [],
      },
    ] as any,
    ...getDefaultAnimationConfig(),
  };

  // 如果传入了options，则进行深度合并
  if (options) {
    return deepMerge(defaultConfig, options);
  }

  return defaultConfig;
}
