import { defHttp } from '@hiip/internal/utils/http/axios';

/**
 * 获取就诊使用抗菌药物百分率(%)卡片数据
 * /medicalQualityAndSafetyMonitor/getJzsykjywbflCardData
 */
export const getJzsykjywbflCardData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/getJzsykjywbflCardData',
    data,
  });
};
/**
 * 就诊使用抗菌药物百分率(%)-趋势分析
 * /medicalQualityAndSafetyMonitor/jzsykjywbfl/getTrendAnalysisData
 */
export const getJzsykjywbflTrendAnalysisData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/jzsykjywbfl/getTrendAnalysisData',
    data,
  });
};

/**
 * 导出就诊使用抗菌药物百分率(%)-趋势分析数据
 * /medicalQualityAndSafetyMonitor/jzsykjywbfl/exportTrendAnalysisData
 */
export const exportJzsykjywbflTrendAnalysisData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/jzsykjywbfl/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * medicalQualityAndSafetyMonitor/jzsykjywbfl/getTop20ForDepartmentData
 * 就诊使用抗菌药物百分率(%)-科室分析前20名
 */
export const getJzsykjywbflTop20ForDepartmentData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/jzsykjywbfl/getTop20ForDepartmentData',
    data,
  });
};

/**
 * /medicalQualityAndSafetyMonitor/jzsykjywbfl/exportTop20ForDepartmentData
 * 就诊使用抗菌药物百分率(%)-科室分析前20名
 */
export const exportJzsykjywbflTop20ForDepartmentData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/jzsykjywbfl/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * /medicalQualityAndSafetyMonitor/jzsykjywbfl/getTop20ForDoctorData
 * 就诊使用抗菌药物百分率(%)-医生前20名
 */
export const getJzsykjywbflTop20ForDoctorData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/jzsykjywbfl/getTop20ForDoctorData',
    data,
  });
};

/**
 * /medicalQualityAndSafetyMonitor/jzsykjywbfl/exportTop20ForDoctorData
 * 就诊使用抗菌药物百分率(%)-医生前20名
 */
export const exportJzsykjywbflTop20ForDoctorData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/jzsykjywbfl/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * /medicalQualityAndSafetyMonitor/jzsykjywbfl/getAgeGroupData
 * 就诊使用抗菌药物百分率(%)-年龄段
 */
export const getJzsykjywbflAgeGroupData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/jzsykjywbfl/getAgeGroupData',
    data,
  });
};

/**
 * /medicalQualityAndSafetyMonitor/jzsykjywbfl/exportAgeGroupData
 * 就诊使用抗菌药物百分率(%)-年龄段导出
 */
export const exportJzsykjywbflAgeGroupData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/jzsykjywbfl/exportAgeGroupData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * /medicalQualityAndSafetyMonitor/jzsykjywbfl/getRegistrationTypeData
 * 就诊使用抗菌药物百分率(%)-挂号类别
 */
export const getJzsykjywbflRegistrationTypeData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/jzsykjywbfl/getRegistrationTypeData',
    data,
  });
};

/**
 * /medicalQualityAndSafetyMonitor/jzsykjywbfl/exportRegistrationTypeData
 * 就诊使用抗菌药物百分率(%)-挂号类别导出
 */
export const exportJzsykjywbflRegistrationTypeData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/jzsykjywbfl/exportRegistrationTypeData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * /medicalQualityAndSafetyMonitor/getJzsyzsywbflCardData
 * 获取就诊使用注射药物百分率(%)卡片数据
 */

export const getJzsyzsywbflCardData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/getJzsyzsywbflCardData',
    data,
  });
};
/**
 * /medicalQualityAndSafetyMonitor/jzsyzsywbfl/getTrendAnalysisData
 * 获取就诊使用注射药物百分率(%) - 趋势分析
 */
export const getJzsyzsywbflTrendAnalysisData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/jzsyzsywbfl/getTrendAnalysisData',
    data,
  });
};
/**
 * /medicalQualityAndSafetyMonitor/jzsyzsywbfl/exportTrendAnalysisData
 * 获取就诊使用注射药物百分率(%) - 趋势导出
 */
export const exportJzsyzsywbflTrendAnalysisData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/jzsyzsywbfl/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
/**
 * /medicalQualityAndSafetyMonitor/jzsyzsywbfl/getTop20ForDepartmentData
 * 获取就诊使用注射药物百分率(%) - 科室分析前20名
 */
export const getJzsyzsywbflTop20ForDepartmentData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/jzsyzsywbfl/getTop20ForDepartmentData',
    data,
  });
};

/**
 * /medicalQualityAndSafetyMonitor/jzsyzsywbfl/exportTop20ForDepartmentData
 *  获取就诊使用注射药物百分率(%) - 科室分析前20名 - 导出
 */
export const exportJzsyzsywbflTop20ForDepartmentData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/jzsyzsywbfl/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * /medicalQualityAndSafetyMonitor/jzsyzsywbfl/getTop20ForDoctorData
 * 获取就诊使用注射药物百分率(%) - 医生前20名
 */
export const getJzsyzsywbflTop20ForDoctorData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/jzsyzsywbfl/getTop20ForDoctorData',
    data,
  });
};

/**
 * /medicalQualityAndSafetyMonitor/jzsyzsywbfl/exportTop20ForDoctorData
 * 获取就诊使用注射药物百分率(%) - 医生前20名 - 导出
 */
export const exportJzsyzsywbflTop20ForDoctorData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/jzsyzsywbfl/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * /medicalQualityAndSafetyMonitor/jzsyzsywbfl/getAgeGroupData
 * 获取就诊使用注射药物百分率(%) - 年龄段
 */
export const getJzsyzsywbflAgeGroupData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/jzsyzsywbfl/getAgeGroupData',
    data,
  });
};

/**
 * /medicalQualityAndSafetyMonitor/jzsyzsywbfl/exportAgeGroupData
 * 获取就诊使用注射药物百分率(%) - 年龄段 - 导出
 */
export const exportJzsyzsywbflAgeGroupData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/jzsyzsywbfl/exportAgeGroupData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * /medicalQualityAndSafetyMonitor/jzsyzsywbfl/getRegistrationTypeData
 * 获取就诊使用注射药物百分率(%) - 挂号类别
 */
export const getJzsyzsywbflRegistrationTypeData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/jzsyzsywbfl/getRegistrationTypeData',
    data,
  });
};

/**
 * /medicalQualityAndSafetyMonitor/jzsyzsywbfl/exportRegistrationTypeData
 * 获取就诊使用注射药物百分率(%) - 挂号类别 - 导出
 */
export const exportJzsyzsywbflRegistrationTypeData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/jzsyzsywbfl/exportRegistrationTypeData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 住院患者人均使用抗菌药物费用(%) - 卡片数据
 * /medicalQualityAndSafetyMonitor/getZyhzrjsykjywfyCardData
 */
export const getZyhzrjsykjywfyCardData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/getZyhzrjsykjywfyCardData',
    data,
  });
};

/**
 * 住院患者人均使用抗菌药物费用(%) - 趋势图
 * /medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/getTrendAnalysisData
 */
export const getZyhzrjsykjywfyTrendAnalysisData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/getTrendAnalysisData',
    data,
  });
};

/**
 * 住院患者人均使用抗菌药物费用(%) - 趋势图 - 导出
 * /medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/exportTrendAnalysisData
 */
export const exportZyhzrjsykjywfyTrendAnalysisData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 住院患者人均使用抗菌药物费用(%) - 科室前10
 * /medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/getTop20ForDepartmentData
 */
export const getZyhzrjsykjywfyTop20ForDepartmentData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/getTop20ForDepartmentData',
    data,
  });
};

/**
 * 住院患者人均使用抗菌药物费用(%) - 科室前10 - 导出
 * /medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/exportTop20ForDepartmentData
 */
export const exportZyhzrjsykjywfyTop20ForDepartmentData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 住院患者人均使用抗菌药物费用(%) - 医生前10
 * /medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/getTop20ForDoctorData
 */
export const getZyhzrjsykjywfyTop20ForDoctorData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/getTop20ForDoctorData',
    data,
  });
};

/**
 * 住院患者人均使用抗菌药物费用(%) - 医生前10 - 导出
 * /medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/exportTop20ForDoctorData
 */
export const exportZyhzrjsykjywfyTop20ForDoctorData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 住院患者人均使用抗菌药物费用(%) - 年龄段
 * /medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/getAgeGroupData
 */
export const getZyhzrjsykjywfyAgeGroupData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/getAgeGroupData',
    data,
  });
};

/**
 * 住院患者人均使用抗菌药物费用(%) - 年龄段 - 导出
 * /medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/exportAgeGroupData
 */
export const exportZyhzrjsykjywfyAgeGroupData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/zyhzrjsykjywfy/exportAgeGroupData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 麻醉开始后24小时内死亡患者数(%) - 卡片数据
 * /medicalQualityAndSafetyMonitor/getMzksh24xsnswhzsCardData
 */
export const getMzksh24xsnswhzsCardData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/getMzksh24xsnswhzsCardData',
    data,
  });
};

/**
 * 麻醉开始后24小时内死亡患者数(%) - 趋势图
 * /medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/getTrendAnalysisData
 */
export const getMzksh24xsnswhzsTrendAnalysisData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/getTrendAnalysisData',
    data,
  });
};

/**
 * 麻醉开始后24小时内死亡患者数(%) - 趋势图 - 导出
 * /medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/exportTrendAnalysisData
 */
export const exportMzksh24xsnswhzsTrendAnalysisData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 麻醉开始后24小时内死亡患者数(%) - 科室前10
 * /medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/getTop20ForDepartmentData
 */
export const getMzksh24xsnswhzsTop20ForDepartmentData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/getTop20ForDepartmentData',
    data,
  });
};

/**
 * 麻醉开始后24小时内死亡患者数(%) - 科室前10 - 导出
 * /medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/exportTop20ForDepartmentData
 */
export const exportMzksh24xsnswhzsTop20ForDepartmentData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 麻醉开始后24小时内死亡患者数(%) - 医生前10
 * /medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/getTop20ForDoctorData
 */
export const getMzksh24xsnswhzsTop20ForDoctorData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/getTop20ForDoctorData',
    data,
  });
};

/**
 * 麻醉开始后24小时内死亡患者数(%) - 医生前10 - 导出
 * /medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/exportTop20ForDoctorData
 */
export const exportMzksh24xsnswhzsTop20ForDoctorData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 麻醉开始后24小时内死亡患者数(%) - 年龄段
 * /medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/getAgeGroupData
 */
export const getMzksh24xsnswhzsAgeGroupData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/getAgeGroupData',
    data,
  });
};

/**
 * 麻醉开始后24小时内死亡患者数(%) - 年龄段 - 导出
 * /medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/exportAgeGroupData
 */
export const exportMzksh24xsnswhzsAgeGroupData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/mzksh24xsnswhzs/exportAgeGroupData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 各ASA分级麻醉患者总数 - 卡片数据
 * /medicalQualityAndSafetyMonitor/getGsasfjmzhzzsCardData
 */
export const getGsasfjmzhzzsCardData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/getGsasfjmzhzzsCardData',
    data,
  });
};

/**
 * 各ASA分级麻醉患者总数 - 趋势图
 * /medicalQualityAndSafetyMonitor/gsasfjmzhzzs/getTrendAnalysisData
 */
export const getGsasfjmzhzzsTrendAnalysisData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/gsasfjmzhzzs/getTrendAnalysisData',
    data,
  });
};

/**
 * 各ASA分级麻醉患者总数 - 趋势图 - 导出
 * /medicalQualityAndSafetyMonitor/gsasfjmzhzzs/exportTrendAnalysisData
 */
export const exportGsasfjmzhzzsTrendAnalysisData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/gsasfjmzhzzs/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 各ASA分级麻醉患者总数 - 科室前10
 * /medicalQualityAndSafetyMonitor/gsasfjmzhzzs/getTop20ForDepartmentData
 */
export const getGsasfjmzhzzsTop20ForDepartmentData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/gsasfjmzhzzs/getTop20ForDepartmentData',
    data,
  });
};

/**
 * 各ASA分级麻醉患者总数 - 科室前10 - 导出
 * /medicalQualityAndSafetyMonitor/gsasfjmzhzzs/exportTop20ForDepartmentData
 */
export const exportGsasfjmzhzzsTop20ForDepartmentData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/gsasfjmzhzzs/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 各ASA分级麻醉患者总数 - 医生前10
 * /medicalQualityAndSafetyMonitor/gsasfjmzhzzs/getTop20ForDoctorData
 */
export const getGsasfjmzhzzsTop20ForDoctorData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/gsasfjmzhzzs/getTop20ForDoctorData',
    data,
  });
};

/**
 * 各ASA分级麻醉患者总数 - 医生前10 - 导出
 * /medicalQualityAndSafetyMonitor/gsasfjmzhzzs/exportTop20ForDoctorData
 */
export const exportGsasfjmzhzzsTop20ForDoctorData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/gsasfjmzhzzs/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 各ASA分级麻醉患者总数 - 年龄段
 * /medicalQualityAndSafetyMonitor/gsasfjmzhzzs/getAgeGroupData
 */
export const getGsasfjmzhzzsAgeGroupData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/gsasfjmzhzzs/getAgeGroupData',
    data,
  });
};

/**
 * 各ASA分级麻醉患者总数 - 年龄段 - 导出
 * /medicalQualityAndSafetyMonitor/gsasfjmzhzzs/exportAgeGroupData
 */
export const exportGsasfjmzhzzsAgeGroupData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/gsasfjmzhzzs/exportAgeGroupData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 抗菌药物处方数/每百张门诊处方 - 卡片数据
 * /medicalQualityAndSafetyMonitor/getKjywcflCardData
 */
export const getKjywcflCardData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/getKjywcflCardData',
    data,
  });
};

/**
 * 抗菌药物处方数/每百张门诊处方 - 趋势图
 * /medicalQualityAndSafetyMonitor/kjywcfl/getTrendAnalysisData
 */
export const getKjywcflTrendAnalysisData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/kjywcfl/getTrendAnalysisData',
    data,
  });
};

/**
 * 抗菌药物处方数/每百张门诊处方 - 趋势图-导出
 * /medicalQualityAndSafetyMonitor/kjywcfl/exportTrendAnalysisData
 */
export const exportKjywcflTrendAnalysisData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/kjywcfl/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 抗菌药物处方数/每百张门诊处方 - 科室前10
 * /medicalQualityAndSafetyMonitor/kjywcfl/getTop20ForDepartmentData
 */
export const getKjywcflTop20ForDepartmentData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/kjywcfl/getTop20ForDepartmentData',
    data,
  });
};

/**
 * 抗菌药物处方数/每百张门诊处方 - 科室前10导出
 * /medicalQualityAndSafetyMonitor/kjywcfl/exportTop20ForDepartmentData
 */
export const exportKjywcflTop20ForDepartmentData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/kjywcfl/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 抗菌药物处方数/每百张门诊处方 - 医生前10
 * /medicalQualityAndSafetyMonitor/kjywcfl/getTop20ForDoctorData
 */
export const getKjywcflTop20ForDoctorData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/kjywcfl/getTop20ForDoctorData',
    data,
  });
};

/**
 * 抗菌药物处方数/每百张门诊处方 - 医生前10导出
 * /medicalQualityAndSafetyMonitor/kjywcfl/exportTop20ForDoctorData
 */
export const exportKjywcflTop20ForDoctorData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/kjywcfl/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 抗菌药物处方数/每百张门诊处方 - 年龄段
 * /medicalQualityAndSafetyMonitor/kjywcfl/getTop20ForAgeGroupData
 */
export const getKjywcflTop20ForAgeGroupData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/kjywcfl/getTop20ForAgeGroupData',
    data,
  });
};

/**
 * 抗菌药物处方数/每百张门诊处方 - 年龄段导出
 * /medicalQualityAndSafetyMonitor/kjywcfl/exportAgeGroupData
 */
export const exportKjywcflTop20ForAgeGroupData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/kjywcfl/exportAgeGroupData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 抗菌药物处方数/每百张门诊处方 - 挂号类别
 * /medicalQualityAndSafetyMonitor/kjywcfl/getRegistrationTypeData
 */
export const getKjywcflRegistrationTypeData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/kjywcfl/getRegistrationTypeData',
    data,
  });
};

/**
 * 抗菌药物处方数/每百张门诊处方 - 挂号类别导出
 * /medicalQualityAndSafetyMonitor/kjywcfl/exportRegistrationTypeData
 */
export const exportKjywcflRegistrationTypeData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/kjywcfl/exportRegistrationTypeData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 抗菌药物占西药出库总金额比 - 卡片数据
 * /medicalQualityAndSafetyMonitor/getKjywzxyckzjebzCardData
 */
export const getKjywzxyckzjebzCardData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/getKjywzxyckzjebzCardData',
    data,
  });
};

/**
 * 抗菌药物占西药出库总金额比 - 趋势图
 * /medicalQualityAndSafetyMonitor/kjywzxyckjebz/getTrendAnalysisData
 */
export const getKjywzxyckjebzTrendAnalysisData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/kjywzxyckjebz/getTrendAnalysisData',
    data,
  });
};

/**
 * 抗菌药物占西药出库总金额比 - 趋势图-导出
 * /medicalQualityAndSafetyMonitor/kjywzxyckjebz/exportTrendAnalysisData
 */
export const exportKjywzxyckjebzTrendAnalysisData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/kjywzxyckjebz/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 抗菌药物占西药出库总金额比 - 科室前10
 * /medicalQualityAndSafetyMonitor/kjywzxyckjebz/getTop20ForDepartmentData
 */
export const getKjywzxyckjebzTop20ForDepartmentData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/kjywzxyckjebz/getTop20ForDepartmentData',
    data,
  });
};

/**
 * 抗菌药物占西药出库总金额比 - 科室前10导出
 * /medicalQualityAndSafetyMonitor/kjywzxyckjebz/exportTop20ForDepartmentData
 */
export const exportKjywzxyckjebzTop20ForDepartmentData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/kjywzxyckjebz/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 死亡人数(手术) - 卡片数据
 * /medicalQualityAndSafetyMonitor/getSsswrsCardData
 */
export const getSsswrsCardData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/getSsswrsCardData',
    data,
  });
};

/**
 * 死亡人数(手术) - 趋势图
 * /medicalQualityAndSafetyMonitor/ssswrs/getTrendAnalysisData
 */
export const getSsswrsTrendAnalysisData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/ssswrs/getTrendAnalysisData',
    data,
  });
};

/**
 * 死亡人数(手术) - 趋势图-导出
 * /medicalQualityAndSafetyMonitor/ssswrs/exportTrendAnalysisData
 */
export const exportSsswrsTrendAnalysisData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/ssswrs/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 死亡人数(手术) - 科室前10
 * /medicalQualityAndSafetyMonitor/ssswrs/getTop20ForDepartmentData
 */
export const getSsswrsTop20ForDepartmentData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/ssswrs/getTop20ForDepartmentData',
    data,
  });
};

/**
 * 死亡人数(手术) - 科室前10导出
 * /medicalQualityAndSafetyMonitor/ssswrs/exportTop20ForDepartmentData
 */
export const exportSsswrsTop20ForDepartmentData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/ssswrs/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 死亡人数(手术) - 医生前10
 * /medicalQualityAndSafetyMonitor/ssswrs/getTop20ForDoctorData
 */
export const getSsswrsTop20ForDoctorData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/ssswrs/getTop20ForDoctorData',
    data,
  });
};

/**
 * 死亡人数(手术) - 医生前10导出
 * /medicalQualityAndSafetyMonitor/ssswrs/exportTop20ForDoctorData
 */
export const exportSsswrsTop20ForDoctorData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/ssswrs/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 死亡人数(手术) - 年龄段
 * /medicalQualityAndSafetyMonitor/ssswrs/getAgeGroupData
 */
export const getSsswrsAgeGroupData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/ssswrs/getAgeGroupData',
    data,
  });
};

/**
 * 死亡人数(手术) - 年龄段导出
 * /medicalQualityAndSafetyMonitor/ssswrs/exportAgeGroupData
 */
export const exportSsswrsAgeGroupData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/ssswrs/exportAgeGroupData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 麻醉总例数 - 卡片数据
 * /medicalQualityAndSafetyMonitor/getMzzlsCardData
 */
export const getMzzlsCardData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/getMzzlsCardData',
    data,
  });
};

/**
 * 麻醉总例数 - 趋势图
 * /medicalQualityAndSafetyMonitor/mzzls/getTrendAnalysisData
 */
export const getMzzlsTrendAnalysisData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/mzzls/getTrendAnalysisData',
    data,
  });
};

/**
 * 麻醉总例数 - 趋势图导出
 * /medicalQualityAndSafetyMonitor/mzzls/exportTrendAnalysisData
 */
export const exportMzzlsTrendAnalysisData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/mzzls/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 麻醉总例数 - 科室前10
 * /medicalQualityAndSafetyMonitor/mzzls/getTop20ForDepartmentData
 */
export const getMzzlsTop20ForDepartmentData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/mzzls/getTop20ForDepartmentData',
    data,
  });
};

/**
 * 麻醉总例数 - 科室前10导出
 * /medicalQualityAndSafetyMonitor/mzzls/exportTop20ForDepartmentData
 */
export const exportMzzlsTop20ForDepartmentData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/mzzls/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 麻醉总例数 - 医生前10
 * /medicalQualityAndSafetyMonitor/mzzls/getTop20ForDoctorData
 */
export const getMzzlsTop20ForDoctorData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/mzzls/getTop20ForDoctorData',
    data,
  });
};

/**
 * 麻醉总例数 - 医生前10导出
 * /medicalQualityAndSafetyMonitor/mzzls/exportTop20ForDoctorData
 */
export const exportMzzlsTop20ForDoctorData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/mzzls/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 麻醉总例数 - 年龄段
 * /medicalQualityAndSafetyMonitor/mzzls/getAgeGroupData
 */
export const getMzzlsAgeGroupData = (data) => {
  return defHttp.post({
    url: '/medicalQualityAndSafetyMonitor/mzzls/getAgeGroupData',
    data,
  });
};

/**
 * 麻醉总例数 - 年龄段导出
 * /medicalQualityAndSafetyMonitor/mzzls/exportAgeGroupData
 */
export const exportMzzlsAgeGroupData = (data) => {
  return defHttp.post(
    {
      url: '/medicalQualityAndSafetyMonitor/mzzls/exportAgeGroupData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
