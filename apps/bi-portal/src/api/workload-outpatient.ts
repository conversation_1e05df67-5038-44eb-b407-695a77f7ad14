import { defHttp } from '@hiip/internal/utils/http/axios';
import type { StatRequestParams } from './common';

/** 门诊工作负荷分析-获取顶部卡片数据 */
export const getTopCardData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/getTopCardData',
    data,
  });
};

/** 门诊工作负荷分析-获取急诊挂号人次数卡片数据 */
export const getJzghrcsCardData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/getJzghrcsCardData',
    data,
  });
};

/** 门诊工作负荷分析-获取门急诊检检检查费用卡片数据 */
export const getMjzjyjcfyCardData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/getMjzjyjcfyCardData',
    data,
  });
};

/** 门诊工作负荷分析-获取门急诊药品费用卡片数据 */
export const getMjzypfyCardData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/getMjzypfyCardData',
    data,
  });
};

/** 门诊工作负荷分析-获取门诊挂号人次数卡片数据 */
export const getMzghrcsCardData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/getMzghrcsCardData',
    data,
  });
};

/** 门诊挂号人次数-趋势分析 */
export const getTrendAnalysisDataForMzghrcs = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/mzghrcs/getTrendAnalysisData',
    data,
  });
};

/** 导出门诊挂号人次数-趋势分析数据 */
export const exportTrendAnalysisDataForMzghrcs = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/mzghrcs/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 门诊挂号人次数-科室分析前20名 */
export const getTop20ForDepartmentDataForMzghrcs = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/mzghrcs/getTop20ForDepartmentData',
    data,
  });
};

/** 导出门诊挂号人次数-科室分析前20名数据 */
export const exportTop20ForDepartmentDataForMzghrcs = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/mzghrcs/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
/** 门诊挂号人次数-处方类型维度分析 /workload/outpatient/mzghrcs/getPrescriptionData*/
export const getPrescriptionData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/mzghrcs/getPrescriptionData',
    data,
  });
};
/** 门诊挂号人次数-处方类型维度分析导出 /workload/outpatient/mzghrcs/exportPrescriptionData*/
export const exportPrescriptionData = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/mzghrcs/exportPrescriptionData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
/** 门诊挂号人次数-医生前20名 /workload/outpatient/mzghrcs/getTop20ForDoctorData*/
export const getTop20ForDoctorDataForMzghrcs = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/mzghrcs/getTop20ForDoctorData',
    data,
  });
};
/** 门诊挂号人次数-医生前20名导出 /workload/outpatient/mzghrcs/exportTop20ForDoctorData*/
export const exportTop20ForDoctorDataForMzghrcs = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/mzghrcs/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 急诊挂号人次数-趋势分析 */
export const getTrendAnalysisDataForJzghrcs = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/jzghrcs/getTrendAnalysisData',
    data,
  });
};

/** 导出急诊挂号人次数-趋势分析数据 */
export const exportTrendAnalysisDataForJzghrcs = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/jzghrcs/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 急诊挂号人次数-科室分析前20名 */
export const getTop20ForDepartmentDataForJzghrcs = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/jzghrcs/getTop20ForDepartmentData',
    data,
  });
};

/** 导出急诊挂号人次数-科室分析前20名数据 */
export const exportTop20ForDepartmentDataForJzghrcs = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/jzghrcs/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 急诊挂号人次数-处方类型维度分析 /workload/outpatient/jzghrcs/getPrescriptionData */
export const getPrescriptionDataForJzghrcs = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/jzghrcs/getPrescriptionData',
    data,
  });
};
/** 急诊挂号人次数-处方类型维度分析导出  /workload/outpatient/jzghrcs/exportPrescriptionData*/
export const exportPrescriptionDataForJzghrcs = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/jzghrcs/exportPrescriptionData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
/** 急诊挂号人次数-医生前20名 /workload/outpatient/jzghrcs/getTop20ForDoctorData*/
export const getTop20ForDoctorDataForJzghrcs = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/jzghrcs/getTop20ForDoctorData',
    data,
  });
};
/** 急诊挂号人次数-医生前20名导出 /workload/outpatient/jzghrcs/exportTop20ForDoctorData*/
export const exportTop20ForDoctorDataForJzghrcs = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/jzghrcs/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 门急诊检检检查费用-趋势分析 */
export const getTrendAnalysisDataForMjzjyjcfy = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/mjzjyjcfy/getTrendAnalysisData',
    data,
  });
};

/** 导出门急诊检检检查费用-趋势分析数据 */
export const exportTrendAnalysisDataForMjzjyjcfy = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/mjzjyjcfy/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 门急诊检检检查费用-科室分析前20名 */
export const getTop20ForDepartmentDataForMjzjyjcfy = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/mjzjyjcfy/getTop20ForDepartmentData',
    data,
  });
};

/** 导出门急诊检检检查费用-科室分析前20名数据 */
export const exportTop20ForDepartmentDataForMjzjyjcfy = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/mjzjyjcfy/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 门急诊检检检查费用-处方类型维度分析 /workload/outpatient/mjzjyjcfy/getPrescriptionData*/
export const getPrescriptionDataForMjzjyjcfy = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/mjzjyjcfy/getPrescriptionData',
    data,
  });
};
/** 门急诊检检检查费用-处方类型维度分析导出 /workload/outpatient/mjzjyjcfy/exportPrescriptionData*/
export const exportPrescriptionDataForMjzjyjcfy = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/mjzjyjcfy/exportPrescriptionData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
/** 门急诊检检检查费用-医生前20名 /workload/outpatient/mjzjyjcfy/getTop20ForDoctorData*/
export const getTop20ForDoctorDataForMjzjyjcfy = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/mjzjyjcfy/getTop20ForDoctorData',
    data,
  });
};
/** 门急诊检检检查费用-医生前20名导出 /workload/outpatient/mjzjyjcfy/exportTop20ForDoctorData*/
export const exportTop20ForDoctorDataForMjzjyjcfy = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/mjzjyjcfy/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 门急诊药品费用-趋势分析 */
export const getTrendAnalysisDataForMjzypfy = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/mjzypfy/getTrendAnalysisData',
    data,
  });
};

/** 导出门急诊药品费用-趋势分析数据 */
export const exportTrendAnalysisDataForMjzypfy = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/mjzypfy/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 门急诊药品费用-科室分析前20名 */
export const getTop20ForDepartmentDataForMjzypfy = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/mjzypfy/getTop20ForDepartmentData',
    data,
  });
};

/** 导出门急诊药品费用-科室分析前20名数据 */
export const exportTop20ForDepartmentDataForMjzypfy = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/mjzypfy/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
/** 门急诊药品费用-处方类型维度分析 /workload/outpatient/mjzypfy/getPrescriptionData*/
export const getPrescriptionDataForMjzypfy = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/mjzypfy/getPrescriptionData',
    data,
  });
};
/** 门急诊药品费用-处方类型维度分析导出 /workload/outpatient/mjzypfy/exportPrescriptionData*/
export const exportPrescriptionDataForMjzypfy = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/mjzypfy/exportPrescriptionData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
/** 门急诊药品费用-医生前20名 /workload/outpatient/mjzypfy/getTop20ForDoctorData*/
export const getTop20ForDoctorDataForMjzypfy = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workload/outpatient/mjzypfy/getTop20ForDoctorData',
    data,
  });
};
/** 门急诊药品费用-医生前20名导出 /workload/outpatient/mjzypfy/exportTop20ForDoctorData*/
export const exportTop20ForDoctorDataForMjzypfy = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workload/outpatient/mjzypfy/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
