import { defHttp } from '@hiip/internal/utils/http/axios';
import type { StatRequestParams } from './common';

/** 获取顶部卡片数据 */
export const getTopCardData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/getTopCardData',
    data,
  });
};

/** 获取床位使用率(%)卡片数据 */
export const getCwsylCardData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/getCwsylCardData',
    data,
  });
};

/** 获取床位周转次数(%)卡片数据 */
export const getCwzzcsCardData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/getCwzzcsCardData',
    data,
  });
};

/** 获取出院患者平均住院日(天)卡片数据 */
export const getCyhzpjzyrCardData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/getCyhzpjzyrCardData',
    data,
  });
};

/** 获取平均每张床位工作日(天)卡片数据 */
export const getPjmzcwgzrCardData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/getPjmzcwgzrCardData',
    data,
  });
};

/** 床位使用率 - 趋势分析 */
export const getTrendAnalysisDataForCwsyl = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cwsyl/getTrendAnalysisData',
    data,
  });
};

/** 导出床位使用率 - 趋势分析数据 */
export const exportTrendAnalysisDataForCwsyl = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/cwsyl/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 床位使用率 - 科室分析前20名 */
export const getTop20ForDepartmentDataForCwsyl = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cwsyl/getTop20ForDepartmentData',
    data,
  });
};

/** 导出床位使用率 - 科室分析前20名数据 */
export const exportTop20ForDepartmentDataForCwsyl = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/cwsyl/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 床位周转次数 - 趋势分析 */
export const getTrendAnalysisDataForCwzzcs = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cwzzcs/getTrendAnalysisData',
    data,
  });
};

/** 导出床位周转次数 - 趋势分析数据 */
export const exportTrendAnalysisDataForCwzzcs = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/cwzzcs/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 床位周转次数 - 科室分析前20名 */
export const getTop20ForDepartmentDataForCwzzcs = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cwzzcs/getTop20ForDepartmentData',
    data,
  });
};

/** 导出床位周转次数 - 科室分析前20名数据 */
export const exportTop20ForDepartmentDataForCwzzcs = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/cwzzcs/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**床位周转次数-医生前20名柱状图接口
 * /workEfficiency/cwzzcs/getTop20ForDoctorData
 */
export const getTop20ForDoctorDataForCwzzcs = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cwzzcs/getTop20ForDoctorData',
    data,
  });
};

/**床位周转次数-导出医生前20名柱状图接口
 * /workEfficiency/cwzzcs/exportTop20ForDoctorData
 */
export const exportTop20ForDoctorDataForCwzzcs = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/cwzzcs/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 平均每张床位工作日 - 趋势分析 */
export const getTrendAnalysisDataForPjmzcwgzr = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/pjmzcwgzr/getTrendAnalysisData',
    data,
  });
};

/** 导出平均每张床位工作日 - 趋势分析数据 */
export const exportTrendAnalysisDataForPjmzcwgzr = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/pjmzcwgzr/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 平均每张床位工作日 - 科室分析前20名 */
export const getTop20ForDepartmentDataForPjmzcwgzr = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/pjmzcwgzr/getTop20ForDepartmentData',
    data,
  });
};

/** 导出平均每张床位工作日 - 科室分析前20名数据 */
export const exportTop20ForDepartmentDataForPjmzcwgzr = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/pjmzcwgzr/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
/**
 * /workEfficiency/pjmzcwgzr/getTop20ForMedicalPaymentTypeData
 * 出院患者平均住院日 - 医疗费用方式分析
 */
export const getTop20ForMedicalPaymentTypeDataForPjmzcwgzr = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/pjmzcwgzr/getTop20ForMedicalPaymentTypeData',
    data,
  });
};
/**
 * /workEfficiency/pjmzcwgzr/exportTop20ForMedicalPaymentTypeData
 * 出院患者平均住院日 - 医疗费用方式分析导出
 */
export const exportTop20ForMedicalPaymentTypeDataForPjmzcwgzr = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/pjmzcwgzr/exportTop20ForMedicalPaymentTypeData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 出院患者平均住院日 - 医生前20名
 * /workEfficiency/pjmzcwgzr/getTop20ForDoctorData
 */
export const getTop20ForDoctorDataForPjmzcwgzr = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/pjmzcwgzr/getTop20ForDoctorData',
    data,
  });
};

/**
 * 出院患者平均住院日 - 医生前20名导出
 */
export const exportTop20ForDoctorDataForPjmzcwgzr = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/pjmzcwgzr/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 出院患者平均住院日 - 趋势分析 */
export const getTrendAnalysisDataForCyhzpjzyr = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cyhzpjzyr/getTrendAnalysisData',
    data,
  });
};

/** 导出出院患者平均住院日 - 趋势分析数据 */
export const exportTrendAnalysisDataForCyhzpjzyr = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/cyhzpjzyr/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 出院患者平均住院日 - 科室分析前20名 */
export const getTop20ForDepartmentDataForCyhzpjzyr = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cyhzpjzyr/getTop20ForDepartmentData',
    data,
  });
};

/** 导出出院患者平均住院日 - 科室分析前20名数据 */
export const exportTop20ForDepartmentDataForCyhzpjzyr = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/cyhzpjzyr/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 出院患者平均住院日-处方类型分析
 * /workEfficiency/cyhzpjzyr/getPrescriptionAnalysisData
 */

export const getPrescriptionAnalysisDataForCyhzpjzyr = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cyhzpjzyr/getPrescriptionAnalysisData',
    data,
  });
};

/**
 * 导出出院患者平均住院日-处方类型分析
 * /workEfficiency/cyhzpjzyr/exportPrescriptionAnalysisData
 */
export const exportPrescriptionAnalysisDataForCyhzpjzyr = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cyhzpjzyr/exportPrescriptionAnalysisData',
    data,
  });
};
/**
 * 出院患者平均住院日-医生前20名柱状图接口
 * /workEfficiency/cyhzpjzyr/getTop20ForDoctorData
 */
export const getTop20ForDoctorDataForCyhzpjzyr = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cyhzpjzyr/getTop20ForDoctorData',
    data,
  });
};

/**
 * 出院患者平均住院日-导出医生前20名柱状图接口
 * /workEfficiency/cyhzpjzyr/exportTop20ForDoctorData
 */
export const exportTop20ForDoctorDataForCyhzpjzyr = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/cyhzpjzyr/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 病区维度分析 */
export const getBedWardAnalysisData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cwsyl/getBedWardAnalysisData',
    data,
  });
};
/** 病区维度分析导出 */
export const exportBedWardAnalysisData = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/cwsyl/exportBedWardAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/** 床位周转次数 医疗费用方式分析 */
export const getTop20ForMedicalPaymentTypeData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cwzzcs/getTop20ForMedicalPaymentTypeData',
    data,
  });
};
/** 床位周转次数 医疗费用方式分析导出 /workEfficiency/cwzzcs/exportTop20ForMedicalPaymentTypeData */
export const exportTop20ForMedicalPaymentTypeData = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/cwzzcs/exportTop20ForMedicalPaymentTypeData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
/** 床位周转次数 医生前20名  /workEfficiency/cwzzcs/getTop20ForDoctorData */
export const getTop20ForDoctorData = (data: StatRequestParams) => {
  return defHttp.post({
    url: '/workEfficiency/cwzzcs/getTop20ForDoctorData',
    data,
  });
};

/** 床位周转次数 医生前20名导出 /workEfficiency/cwzzcs/exportTop20ForDoctorData */
export const exportTop20ForDoctorData = (data: StatRequestParams) => {
  return defHttp.post(
    {
      url: '/workEfficiency/cwzzcs/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
