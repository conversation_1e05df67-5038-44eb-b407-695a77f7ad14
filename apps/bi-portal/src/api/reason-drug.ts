import { defHttp } from '@hiip/internal/utils/http/axios';

/**
 * 获取顶部卡片数据
 * /rationalDrugUseMonitor/getTopCardData
 */
export const getTopCardData = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/getTopCardData', data });
};

/**
 * 获取门诊处方总数(张)卡片数据
 * /rationalDrugUseMonitor/getMzcfzsCardData
 */
export const getMzcfzsCardData = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/getMzcfzsCardData', data });
};

/**
 * 获取抗菌药物处方数-每百张门诊处方(%)卡片数据
 * /rationalDrugUseMonitor/getMzhzkjywcfblCardData
 */
export const getMzhzkjywcfblCardData = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/getMzhzkjywcfblCardData', data });
};

/**
 * 获取注射剂处方数-每百张门诊处方(%)卡片数据
 * /rationalDrugUseMonitor/getMzhzzsjcfblCardData
 */
export const getMzhzzsjcfblCardData = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/getMzhzzsjcfblCardData', data });
};

/**
 * 获取药占比(%)卡片数据
 * /rationalDrugUseMonitor/getYzbCardData
 */
export const getYzbCardData = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/getYzbCardData', data });
};

/**
 * 导出药占比(%)-科室分析前20名数据
 * /rationalDrugUseMonitor/yzb/exportTop20ForDepartmentData
 */
export const exportTop20ForDepartmentData = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/yzb/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 导出药占比(%)-趋势分析数据
 * /rationalDrugUseMonitor/yzb/exportTrendAnalysisData
 */
export const exportTrendAnalysisData = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/yzb/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 药占比(%)-科室分析前20名
 * /rationalDrugUseMonitor/yzb/getTop20ForDepartmentData
 */
export const getTop20ForDepartmentData = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/yzb/getTop20ForDepartmentData', data });
};

/**
 * 药占比(%)-趋势分析
 * /rationalDrugUseMonitor/yzb/getTrendAnalysisData
 */
export const getTrendAnalysisData = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/yzb/getTrendAnalysisData', data });
};

/**
 *药占比(%)-处方类型维度分析
 POST /rationalDrugUseMonitor/yzb/getPrescriptionAnalysisData
 */
export const getPrescriptionAnalysisDataForYzb = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/yzb/getPrescriptionAnalysisData', data });
};
/**
 * 导出药占比(%)-处方类型维度分析数据
 * /rationalDrugUseMonitor/yzb/exportPrescriptionAnalysisData
 */
export const exportPrescriptionAnalysisDataForYzb = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/yzb/exportPrescriptionAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 药占比(%)-医生前20名
 */
export const getTop20ForDoctorDataForYzb = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/yzb/getTop20ForDoctorData', data });
};

/**
 * 导出药占比(%)-医生前20名数据
 * /rationalDrugUseMonitor/yzb/exportTop20ForDoctorData
 */
export const exportTop20ForDoctorDataForYzb = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/yzb/exportTop20ForDoctorData', data });
};

/**
 * 注射剂处方数-每百张门诊处方(%)-趋势分析
 * /rationalDrugUseMonitor/mzhzzsjcfbl/getTrendAnalysisData
 */
export const getTrendAnalysisDataForMzhzzsjcfbl = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/mzhzzsjcfbl/getTrendAnalysisData', data });
};
/**
 * 注射剂处方数-每百张门诊处方(%)-科室分析前20名
 * /rationalDrugUseMonitor/mzhzzsjcfbl/getTop20ForDepartmentData
 */
export const getTop20ForDepartmentDataForMzhzzsjcfbl = (data) => {
  return defHttp.post({
    url: '/rationalDrugUseMonitor/mzhzzsjcfbl/getTop20ForDepartmentData',
    data,
  });
};
/**
 * 导出注射剂处方数-每百张门诊处方(%)-趋势分析数据
 * /rationalDrugUseMonitor/mzhzzsjcfbl/exportTrendAnalysisData
 */
export const exportTrendAnalysisDataForMzhzzsjcfbl = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/mzhzzsjcfbl/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 导出注射剂处方数-每百张门诊处方(%)-科室分析前20名数据
 * /rationalDrugUseMonitor/mzhzzsjcfbl/exportTop20ForDepartmentData
 */
export const exportTop20ForDepartmentDataForMzhzzsjcfbl = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/mzhzzsjcfbl/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**注射剂处方数-处方类型维度分析 /rationalDrugUseMonitor/mzhzzsjcfbl/getPrescriptionAnalysisData */
export const getPrescriptionAnalysisDataForMzhzzsjcfbl = (data) => {
  return defHttp.post({
    url: '/rationalDrugUseMonitor/mzhzzsjcfbl/getPrescriptionAnalysisData',
    data,
  });
};
/**注射剂处方数-处方类型维度分析导出 /rationalDrugUseMonitor/mzhzzsjcfbl/exportPrescriptionAnalysisData */
export const exportPrescriptionAnalysisDataForMzhzzsjcfbl = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/mzhzzsjcfbl/exportPrescriptionAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
/**注射剂处方数-医生前20名 /rationalDrugUseMonitor/mzhzzsjcfbl/getTop20ForDoctorData */
export const getTop20ForDoctorDataForMzhzzsjcfbl = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/mzhzzsjcfbl/getTop20ForDoctorData', data });
};

/**注射剂处方数-医生前20名导出 /rationalDrugUseMonitor/mzhzzsjcfbl/exportTop20ForDoctorData */
export const exportTop20ForDoctorDataForMzhzzsjcfbl = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/mzhzzsjcfbl/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 抗菌药物处方数-每百张门诊处方(%)-趋势分析
 * /rationalDrugUseMonitor/mzhzkjywcfbl/getTrendAnalysisData
 */
export const getTrendAnalysisDataForMzhzkjywcfbl = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/mzhzkjywcfbl/getTrendAnalysisData', data });
};

/**
 * 抗菌药物处方数-每百张门诊处方(%)-科室分析前20名
 * /rationalDrugUseMonitor/mzhzkjywcfbl/getTop20ForDepartmentData
 */
export const getTop20ForDepartmentDataForMzhzkjywcfbl = (data) => {
  return defHttp.post({
    url: '/rationalDrugUseMonitor/mzhzkjywcfbl/getTop20ForDepartmentData',
    data,
  });
};

/**
 * 导出抗菌药物处方数-每百张门诊处方(%)-趋势分析数据
 * /rationalDrugUseMonitor/mzhzkjywcfbl/exportTrendAnalysisData
 */
export const exportTrendAnalysisDataForMzhzkjywcfbl = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/mzhzkjywcfbl/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
/**
 * 导出抗菌药物处方数-每百张门诊处方(%)-科室分析前20名数据
 * /rationalDrugUseMonitor/mzhzkjywcfbl/exportTop20ForDepartmentData
 */
export const exportTop20ForDepartmentDataForMzhzkjywcfbl = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/mzhzkjywcfbl/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**抗菌药物处方数-处方类型维度分析 /rationalDrugUseMonitor/mzhzkjywcfbl/getPrescriptionAnalysisData */
export const getPrescriptionAnalysisDataForMzhzkjywcfbl = (data) => {
  return defHttp.post({
    url: '/rationalDrugUseMonitor/mzhzkjywcfbl/getPrescriptionAnalysisData',
    data,
  });
};

/** 抗菌药物处方数-处方类型维度分析导出 /rationalDrugUseMonitor/mzhzkjywcfbl/exportPrescriptionAnalysisData */
export const exportPrescriptionAnalysisDataForMzhzkjywcfbl = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/mzhzkjywcfbl/exportPrescriptionAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**抗菌药物处方数-医生前20名 /rationalDrugUseMonitor/mzhzkjywcfbl/getTop20ForDoctorData */
export const getTop20ForDoctorDataForMzhzkjywcfbl = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/mzhzkjywcfbl/getTop20ForDoctorData', data });
};
/**抗菌药物处方数-医生前20名导出 /rationalDrugUseMonitor/mzhzkjywcfbl/exportTop20ForDoctorData */
export const exportTop20ForDoctorDataForMzhzkjywcfbl = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/mzhzkjywcfbl/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 门诊处方总数-趋势分析
 * /rationalDrugUseMonitor/mzcfzs/getTrendAnalysisData
 */
export const getTrendAnalysisDataForMzcfzs = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/mzcfzs/getTrendAnalysisData', data });
};

/**
 * 门诊处方总数-科室分析前20名
 * /rationalDrugUseMonitor/mzcfzs/getTop20ForDepartmentData
 */
export const getTop20ForDepartmentDataForMzcfzs = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/mzcfzs/getTop20ForDepartmentData', data });
};

/**
 * 导出门诊处方总数-趋势分析数据
 * /rationalDrugUseMonitor/mzcfzs/exportTrendAnalysisData
 */
export const exportTrendAnalysisDataForMzcfzs = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/mzcfzs/exportTrendAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 导出门诊处方总数-科室分析前20名数据
 * /rationalDrugUseMonitor/mzcfzs/exportTop20ForDepartmentData
 */
export const exportTop20ForDepartmentDataForMzcfzs = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/mzcfzs/exportTop20ForDepartmentData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 处方类型维度分析
 * /rationalDrugUseMonitor/mzcfzs/getPrescriptionAnalysisData
 */
export const getPrescriptionAnalysisData = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/mzcfzs/getPrescriptionAnalysisData', data });
};

/**
 * 导出处方类型维度分析数据
 * /rationalDrugUseMonitor/mzcfzs/exportPrescriptionAnalysisData
 */
export const exportPrescriptionAnalysisData = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/mzcfzs/exportPrescriptionAnalysisData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 医生前20名柱状图接口
 * /rationalDrugUseMonitor/mzcfzs/getTop20ForDoctorData
 */
export const getTop20ForDoctorData = (data) => {
  return defHttp.post({ url: '/rationalDrugUseMonitor/mzcfzs/getTop20ForDoctorData', data });
};

/**
 * 导出医生前20名柱状图接口
 * /rationalDrugUseMonitor/mzcfzs/exportTop20ForDoctorData
 */

export const exportTop20ForDoctorData = (data) => {
  return defHttp.post(
    {
      url: '/rationalDrugUseMonitor/mzcfzs/exportTop20ForDoctorData',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
