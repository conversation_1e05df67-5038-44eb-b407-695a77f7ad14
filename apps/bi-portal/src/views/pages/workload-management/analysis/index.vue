<!-- eslint-disable sort-imports -->
<script lang="ts" setup>
  import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
  import { useMessage } from '@hiip/internal/hooks/web/useMessage';
  import { ChartTable } from '/@/components/ChartTable';
  import { StatCard } from '/@/components/StatCard';
  import { useToggle } from '@vueuse/core';
  import { usePageConfig } from '/@/hooks/usePageConfig';
  import {
    type ChartDataItem,
    type WMTabConfig as TabConfig,
    ChartType,
  } from '/@/views/pages/types';
  import { exportUtil } from '@hiip/internal/utils';
  import { createChartConfig } from './chart-factory';
  import {
    // 住院相关API
    getCyrsCardData,
    getPjmwysmrdfdzycrsCardData,
    getZyjyjcfyCardData,
    getZyypfyCardData,
    getTrendAnalysisDataForCyrs,
    exportTrendAnalysisDataForCyrs,
    getTop20ForDepartmentDataForCyrs,
    exportTop20ForDepartmentDataForCyrs,
    getPrescriptionAnalysisData,
    exportPrescriptionAnalysisData,
    getTop20ForDoctorData,
    exportTop20ForDoctorData,
    getTrendAnalysisDataForZyjyjcfy,
    exportTrendAnalysisDataForZyjyjcfy,
    getTop20ForDepartmentDataForZyjyjcfy,
    exportTop20ForDepartmentDataForZyjyjcfy,
    getPrescriptionAnalysisDataForZyjyjcfy,
    exportPrescriptionAnalysisDataForZyjyjcfy,
    getTop20ForDoctorDataForZyjyjcfy,
    exportTop20ForDoctorDataForZyjyjcfy,
    getTrendAnalysisDataForZyypfy,
    exportTrendAnalysisDataForZyypfy,
    getTop20ForDepartmentDataForZyypfy,
    exportTop20ForDepartmentDataForZyypfy,
    getPrescriptionAnalysisDataForZyypfy,
    exportPrescriptionAnalysisDataForZyypfy,
    getTop20ForDoctorDataForZyypfy,
    exportTop20ForDoctorDataForZyypfy,
    getTrendAnalysisDataForPjmwysmrdfzycrs,
    exportTrendAnalysisDataForPjmwysmrdfzycrs,
    getTop20ForDepartmentDataForPjmwysmrdfzycrs,
    exportTop20ForDepartmentDataForPjmwysmrdfzycrs,
  } from '/@/api/workload-inpatient';
  import {
    // 门诊相关API
    getJzghrcsCardData,
    getMjzjyjcfyCardData,
    getMjzypfyCardData,
    getMzghrcsCardData,
    getTrendAnalysisDataForMzghrcs,
    exportTrendAnalysisDataForMzghrcs,
    getTop20ForDepartmentDataForMzghrcs,
    exportTop20ForDepartmentDataForMzghrcs,
    getTrendAnalysisDataForJzghrcs,
    exportTrendAnalysisDataForJzghrcs,
    getTop20ForDepartmentDataForJzghrcs,
    exportTop20ForDepartmentDataForJzghrcs,
    getTrendAnalysisDataForMjzjyjcfy,
    exportTrendAnalysisDataForMjzjyjcfy,
    getTop20ForDepartmentDataForMjzjyjcfy,
    exportTop20ForDepartmentDataForMjzjyjcfy,
    getTrendAnalysisDataForMjzypfy,
    exportTrendAnalysisDataForMjzypfy,
    getTop20ForDepartmentDataForMjzypfy,
    exportTop20ForDepartmentDataForMjzypfy,
    exportPrescriptionData,
    exportPrescriptionDataForJzghrcs,
    exportPrescriptionDataForMjzjyjcfy,
    exportPrescriptionDataForMjzypfy,
    exportTop20ForDoctorDataForJzghrcs,
    exportTop20ForDoctorDataForMjzjyjcfy,
    exportTop20ForDoctorDataForMjzypfy,
    exportTop20ForDoctorDataForMzghrcs,
    getPrescriptionData,
    getPrescriptionDataForJzghrcs,
    getPrescriptionDataForMjzjyjcfy,
    getPrescriptionDataForMjzypfy,
    getTop20ForDoctorDataForJzghrcs,
    getTop20ForDoctorDataForMjzjyjcfy,
    getTop20ForDoctorDataForMjzypfy,
    getTop20ForDoctorDataForMzghrcs,
  } from '/@/api/workload-outpatient';
  import { keepDecimalString } from '/@/utils';
  import CommonFilter from '/@/components/CommonFilter/index.vue';

  const { createMessage } = useMessage();
  const { isTabVisible, isChartVisible, getChartColSpan } = usePageConfig();

  // 住院/门诊切换状态
  const selected = ref('1');
  const toggleSelected = (type: string) => {
    selected.value = type;
  };

  // 统一的 tabMap 配置
  const tabMap: TabConfig[] = [
    // 住院指标
    {
      id: 1,
      title: '出院人数（人）',
      key: 'cyrs',
      icon: 'mz|svg',
      type: '1',
      cardApi: getCyrsCardData,
      cardDataKeys: {
        value: 'cyrs',
        contemporaryValue: 'cyrsContemporaryValue',
        growthRate: 'cyrsGrowthRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getTrendAnalysisDataForCyrs,
          exportApi: exportTrendAnalysisDataForCyrs,
        },
        [ChartType.DEPARTMENT]: {
          api: getTop20ForDepartmentDataForCyrs,
          exportApi: exportTop20ForDepartmentDataForCyrs,
        },
        [ChartType.PRESCRIPTION]: {
          api: getPrescriptionAnalysisData,
          exportApi: exportPrescriptionAnalysisData,
        },
        [ChartType.DOCTOR]: {
          api: getTop20ForDoctorData,
          exportApi: exportTop20ForDoctorData,
        },
      },
    },
    {
      id: 2,
      title: '住院检验检查费用（元）',
      key: 'zyjyjcfy',
      icon: 'mz|svg',
      type: '1',
      cardApi: getZyjyjcfyCardData,
      cardDataKeys: {
        value: 'zyjyjcfy',
        contemporaryValue: 'zyjyjcfyContemporaryValue',
        growthRate: 'zyjyjcfyGrowthRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getTrendAnalysisDataForZyjyjcfy,
          exportApi: exportTrendAnalysisDataForZyjyjcfy,
        },
        [ChartType.DEPARTMENT]: {
          api: getTop20ForDepartmentDataForZyjyjcfy,
          exportApi: exportTop20ForDepartmentDataForZyjyjcfy,
        },
        [ChartType.PRESCRIPTION]: {
          api: getPrescriptionAnalysisDataForZyjyjcfy,
          exportApi: exportPrescriptionAnalysisDataForZyjyjcfy,
        },
        [ChartType.DOCTOR]: {
          api: getTop20ForDoctorDataForZyjyjcfy,
          exportApi: exportTop20ForDoctorDataForZyjyjcfy,
        },
      },
    },
    {
      id: 3,
      title: '住院药品费用（元）',
      key: 'zyypfy',
      icon: 'mz|svg',
      type: '1',
      cardApi: getZyypfyCardData,
      cardDataKeys: {
        value: 'zyypfy',
        contemporaryValue: 'zyypfyContemporaryValue',
        growthRate: 'zyypfyGrowthRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getTrendAnalysisDataForZyypfy,
          exportApi: exportTrendAnalysisDataForZyypfy,
        },
        [ChartType.DEPARTMENT]: {
          api: getTop20ForDepartmentDataForZyypfy,
          exportApi: exportTop20ForDepartmentDataForZyypfy,
        },
        [ChartType.PRESCRIPTION]: {
          api: getPrescriptionAnalysisDataForZyypfy,
          exportApi: exportPrescriptionAnalysisDataForZyypfy,
        },
        [ChartType.DOCTOR]: {
          api: getTop20ForDoctorDataForZyypfy,
          exportApi: exportTop20ForDoctorDataForZyypfy,
        },
      },
    },
    {
      id: 4,
      title: '平均每位医师每日担负的住院床日数',
      key: 'pjmwysmrdfzycrs',
      icon: 'mz|svg',
      type: '1',
      cardApi: getPjmwysmrdfdzycrsCardData,
      cardDataKeys: {
        value: 'pjmwysmrdfzycrs',
        contemporaryValue: 'pjmwysmrdfzycrsContemporaryValue',
        growthRate: 'pjmwysmrdfzycrsGrowthRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getTrendAnalysisDataForPjmwysmrdfzycrs,
          exportApi: exportTrendAnalysisDataForPjmwysmrdfzycrs,
        },
        [ChartType.DEPARTMENT]: {
          api: getTop20ForDepartmentDataForPjmwysmrdfzycrs,
          exportApi: exportTop20ForDepartmentDataForPjmwysmrdfzycrs,
        },
      },
    },
    // 门诊指标
    {
      id: 5,
      title: '门诊挂号人次数',
      key: 'mzghrcs',
      icon: 'mz|svg',
      type: '2',
      cardApi: getMzghrcsCardData,
      cardDataKeys: {
        value: 'mzghrcs',
        contemporaryValue: 'mzghrcsContemporaryValue',
        growthRate: 'mzghrcsGrowthRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getTrendAnalysisDataForMzghrcs,
          exportApi: exportTrendAnalysisDataForMzghrcs,
        },
        [ChartType.DEPARTMENT]: {
          api: getTop20ForDepartmentDataForMzghrcs,
          exportApi: exportTop20ForDepartmentDataForMzghrcs,
        },
        [ChartType.PRESCRIPTION]: {
          api: getPrescriptionData,
          exportApi: exportPrescriptionData,
        },
        [ChartType.DOCTOR]: {
          api: getTop20ForDoctorDataForMzghrcs,
          exportApi: exportTop20ForDoctorDataForMzghrcs,
        },
      },
    },
    {
      id: 6,
      title: '急诊挂号人次数',
      key: 'jzghrcs',
      icon: 'mz|svg',
      type: '2',
      cardApi: getJzghrcsCardData,
      cardDataKeys: {
        value: 'jzghrcs',
        contemporaryValue: 'jzghrcsContemporaryValue',
        growthRate: 'jzghrcsGrowthRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getTrendAnalysisDataForJzghrcs,
          exportApi: exportTrendAnalysisDataForJzghrcs,
        },
        [ChartType.DEPARTMENT]: {
          api: getTop20ForDepartmentDataForJzghrcs,
          exportApi: exportTop20ForDepartmentDataForJzghrcs,
        },
        [ChartType.PRESCRIPTION]: {
          api: getPrescriptionDataForJzghrcs,
          exportApi: exportPrescriptionDataForJzghrcs,
        },
        [ChartType.DOCTOR]: {
          api: getTop20ForDoctorDataForJzghrcs,
          exportApi: exportTop20ForDoctorDataForJzghrcs,
        },
      },
    },
    {
      id: 7,
      title: '门急诊检验检查费用',
      key: 'mjzjyjcfy',
      icon: 'mz|svg',
      type: '2',
      cardApi: getMjzjyjcfyCardData,
      cardDataKeys: {
        value: 'mjzjyjcfy',
        contemporaryValue: 'mjzjyjcfyContemporaryValue',
        growthRate: 'mjzjyjcfyGrowthRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getTrendAnalysisDataForMjzjyjcfy,
          exportApi: exportTrendAnalysisDataForMjzjyjcfy,
        },
        [ChartType.DEPARTMENT]: {
          api: getTop20ForDepartmentDataForMjzjyjcfy,
          exportApi: exportTop20ForDepartmentDataForMjzjyjcfy,
        },
        [ChartType.PRESCRIPTION]: {
          api: getPrescriptionDataForMjzjyjcfy,
          exportApi: exportPrescriptionDataForMjzjyjcfy,
        },
        [ChartType.DOCTOR]: {
          api: getTop20ForDoctorDataForMjzjyjcfy,
          exportApi: exportTop20ForDoctorDataForMjzjyjcfy,
        },
      },
    },
    {
      id: 8,
      title: '门急诊药品费用',
      key: 'mjzypfy',
      icon: 'mz|svg',
      type: '2',
      cardApi: getMjzypfyCardData,
      cardDataKeys: {
        value: 'mjzypfy',
        contemporaryValue: 'mjzypfyContemporaryValue',
        growthRate: 'mjzypfyGrowthRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getTrendAnalysisDataForMjzypfy,
          exportApi: exportTrendAnalysisDataForMjzypfy,
        },
        [ChartType.DEPARTMENT]: {
          api: getTop20ForDepartmentDataForMjzypfy,
          exportApi: exportTop20ForDepartmentDataForMjzypfy,
        },
        [ChartType.PRESCRIPTION]: {
          api: getPrescriptionDataForMjzypfy,
          exportApi: exportPrescriptionDataForMjzypfy,
        },
        [ChartType.DOCTOR]: {
          api: getTop20ForDoctorDataForMjzypfy,
          exportApi: exportTop20ForDoctorDataForMjzypfy,
        },
      },
    },
  ];

  const fetchKey = ref(0);

  // 筛选参数
  const filterParams = ref();
  // 当前选中的 tab
  const activeTab = ref(1);

  // 计算可见的Tabs（根据当前选中的住院/门诊类型过滤）
  const visibleTabs = computed(() => {
    return tabMap.filter((tab) => {
      const typeMatch = tab.type === selected.value;
      const configVisible = isTabVisible({ tabId: tab.id, suffix: selected.value });
      return typeMatch && configVisible;
    });
  });

  // 统计数据（动态生成所有可能的键）
  const statsData = reactive<Record<string, string>>({});

  // 初始化统计数据
  tabMap.forEach((tab) => {
    statsData[tab.cardDataKeys.value] = '0';
    statsData[tab.cardDataKeys.contemporaryValue] = '0';
    statsData[tab.cardDataKeys.growthRate] = '0';
  });

  // 重构图表数据结构
  const chartData = reactive<Record<ChartType, ChartDataItem>>({
    [ChartType.TREND]: {
      data: [],
      loading: false,
      exportLoading: false,
    },
    [ChartType.DEPARTMENT]: {
      data: [],
      loading: false,
      exportLoading: false,
    },
    [ChartType.PRESCRIPTION]: {
      data: [],
      loading: false,
      exportLoading: false,
    },
    [ChartType.DOCTOR]: {
      data: [],
      loading: false,
      exportLoading: false,
    },
  });

  const chartDataLoading = computed(() => {
    return Object.values(chartData).some((state) => state.loading || state.exportLoading);
  });

  const exportLoading = reactive<Record<string, boolean>>({});

  // 统一的 props 构建函数
  const createChartProps = (type: ChartType, baseTitle: string, onDownload?: () => void) => {
    const config = createChartConfig(type, baseTitle);
    const chartState = chartData[type];
    return {
      title: config.title,
      options: [
        {
          tab: config.title,
          data: chartState.data,
          columns: config.columns,
          chartOption: config.getChartOption(chartState.data),
        },
      ],
      loading: chartState.loading,
      onDownload,
    };
  };

  const handleExport = async (type: ChartType) => {
    const params = filterParams.value;
    const currentTabConfig = tabMap.find((tab) => tab.id === activeTab.value);
    const chartConfig = currentTabConfig?.charts[type];

    if (!chartConfig?.exportApi) {
      createMessage.warning('该图表不支持导出');
      return;
    }

    exportLoading[type] = true;
    try {
      await exportUtil(chartConfig.exportApi(params));
    } catch (error) {
      createMessage.error('导出失败');
      console.error('导出失败:', error);
    } finally {
      exportLoading[type] = false;
    }
  };

  const charts = computed(() => {
    const currentTabConfig = tabMap.find((tab) => tab.id === activeTab.value);
    const baseTitle = currentTabConfig?.title || '';

    const chartConfigs = [
      {
        key: ChartType.TREND,
        colSpan: getChartColSpan({
          tabId: activeTab.value,
          chartType: ChartType.TREND,
          suffix: selected.value,
        }),
      },
      {
        key: ChartType.DEPARTMENT,
        colSpan: getChartColSpan({
          tabId: activeTab.value,
          chartType: ChartType.DEPARTMENT,
          suffix: selected.value,
        }),
      },
      {
        key: ChartType.PRESCRIPTION,
        colSpan: getChartColSpan({
          tabId: activeTab.value,
          chartType: ChartType.PRESCRIPTION,
          suffix: selected.value,
        }),
      },
      {
        key: ChartType.DOCTOR,
        colSpan: getChartColSpan({
          tabId: activeTab.value,
          chartType: ChartType.DOCTOR,
          suffix: selected.value,
        }),
      },
    ];

    return chartConfigs
      .filter(
        (c) =>
          currentTabConfig?.charts[c.key] &&
          isChartVisible({ tabId: activeTab.value, chartType: c.key, suffix: selected.value }),
      )
      .map((chart) => {
        return {
          ...chart,
          downloadLoading: exportLoading[chart.key],
          props: createChartProps(chart.key, baseTitle, () => handleExport(chart.key)),
        };
      });
  });

  // 为每个指标创建单独的loading状态
  const loadingStates = reactive<Record<string, boolean>>({});
  const toggleFunctions = reactive<Record<string, (value?: boolean) => boolean>>({});

  // 初始化loading状态和toggle函数
  tabMap.forEach((tab) => {
    const [loading, setLoading] = useToggle(false);
    loadingStates[tab.key] = loading.value;
    toggleFunctions[tab.key] = setLoading;

    watch(loading, (newVal) => {
      loadingStates[tab.key] = newVal;
    });
  });

  // 刷新 Top Card 数据
  async function getTopCardDataRefresh() {
    const params = filterParams.value;
    const currentFetchKey = fetchKey.value;

    const promises = visibleTabs.value.map(async (tab) => {
      try {
        toggleFunctions[tab.key](true);
        const result = await tab.cardApi(params);
        if (currentFetchKey === fetchKey.value) {
          statsData[tab.cardDataKeys.value] = keepDecimalString(result.value);
          statsData[tab.cardDataKeys.contemporaryValue] = keepDecimalString(
            result.contemporaryValue,
          );
          statsData[tab.cardDataKeys.growthRate] = keepDecimalString(result.growthRate);
        }
      } catch (error) {
        createMessage.error(`获取${tab.title}数据失败`);
        console.error(`获取${tab.title}数据失败:`, error);
      } finally {
        toggleFunctions[tab.key](false);
      }
    });

    await Promise.all(promises);
  }

  // 统一的数据刷新函数
  const refreshChartData = async (type: ChartType) => {
    try {
      const currentFetchKey = fetchKey.value;
      const params = filterParams.value;
      const currentTabConfig = tabMap.find((tab) => tab.id === activeTab.value);
      const chartConfig = currentTabConfig?.charts[type];

      if (!chartConfig?.api) {
        chartData[type].data = [];
        return;
      }
      chartData[type].loading = true;
      const result = await chartConfig.api(params);
      if (currentFetchKey === fetchKey.value) {
        chartData[type].data = result;
      }
    } catch (error) {
      createMessage.error('获取图表数据失败');
      chartData[type].data = [];
    } finally {
      chartData[type].loading = false;
    }
  };

  // 刷新所有数据
  const refreshData = async (refreshTopCard = true) => {
    fetchKey.value = new Date().valueOf();
    if (refreshTopCard) {
      getTopCardDataRefresh();
    }

    const currentTabConfig = tabMap.find((tab) => tab.id === activeTab.value);

    if (currentTabConfig) {
      Object.entries(currentTabConfig.charts).forEach(([chartType, chartConfig]) => {
        if (
          chartConfig &&
          isChartVisible({
            tabId: activeTab.value,
            chartType: chartType as ChartType,
            suffix: selected.value,
          })
        ) {
          refreshChartData(chartType as ChartType);
        }
      });
    }
  };

  watch(activeTab, (newVal, oldVal) => {
    if (newVal !== oldVal) {
      refreshData(false);
    }
  });

  watch(selected, () => {
    // 切换住院/门诊时，重置activeTab为对应类型的第一个
    const firstTab = visibleTabs.value[0];
    if (firstTab) {
      activeTab.value = firstTab.id;
    }
    nextTick(() => refreshData());
  });

  // 重置筛选条件
  const resetFilter = () => {
    nextTick(refreshData);
  };

  // 页面加载时获取数据
  onMounted(() => {
    refreshData();
  });
</script>

<template>
  <div class="h-full flex flex-col gap-2 bg-[#f5f7fa] overflow-hidden">
    <!-- 筛选条件区 -->
    <div class="bg-white p-4 rounded-lg flex items-center space-x-4">
      <!-- 住院/门诊切换按钮 -->
      <div
        :class="[chartDataLoading ? 'cursor-not-allowed' : 'cursor-pointer', 'mr-4']"
        @click="!chartDataLoading && toggleSelected(selected === '1' ? '2' : '1')"
      >
        {{ selected === '1' ? '住院' : '门急诊' }}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 512 512"
          style="vertical-align: -3px"
        >
          <path
            fill="none"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="32"
            d="m304 48l112 112l-112 112m94.87-112H96m112 304L96 352l112-112m-94 112h302"
          />
        </svg>
      </div>
      <CommonFilter v-model="filterParams" @reset="resetFilter" @search="refreshData" />
    </div>

    <!-- 统计卡片区 -->
    <div class="grid grid-cols-4 gap-2">
      <StatCard
        v-for="tab in visibleTabs"
        :key="tab.id"
        :active="activeTab === tab.id"
        :title="tab.title"
        :value="statsData[tab.cardDataKeys.value]"
        :compare-value="statsData[tab.cardDataKeys.contemporaryValue]"
        :rate="statsData[tab.cardDataKeys.growthRate]"
        :icon="tab.icon"
        :loading="loadingStates[tab.key]"
        @click="activeTab = tab.id"
      />
    </div>

    <!-- 图表区域 -->
    <div class="flex-1 grid grid-cols-24 gap-2 min-h-0 bg-white">
      <div
        v-for="chart in charts"
        :key="chart.key"
        class="p-4 rounded-lg h-[400px]"
        :style="{
          gridColumn: `span ${chart?.colSpan ?? 12} / span ${chart?.colSpan ?? 12}`,
        }"
      >
        <ChartTable v-bind="chart.props" :download-loading="chart.downloadLoading" class="h-full" />
      </div>
    </div>
  </div>
</template>
