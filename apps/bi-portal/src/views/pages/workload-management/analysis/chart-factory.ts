import { getBarLineMixChartOption, getLinesChartOption } from './data';
import type { ChartConfigMapping } from '/@/views/pages/types';

export const createChartConfig = (type: string, baseTitle: string): ChartConfigMapping => {
  const configs: Record<string, ChartConfigMapping> = {
    trend: {
      title: `${baseTitle} - 趋势分析`,
      columns: [
        { dataIndex: 'yearMonth', title: '日期' },
        { dataIndex: 'currentValue', title: '本期值' },
        { dataIndex: 'contemporaryValue', title: '同期值' },
      ],
      getChartOption: (data) =>
        getLinesChartOption({
          data,
          xAxisField: 'yearMonth',
          yAxisField: [
            { field: 'currentValue', name: '本期值' },
            { field: 'contemporaryValue', name: '同期值' },
          ],
        }),
    },
    department: {
      title: `${baseTitle} - 科室前20名`,
      columns: [
        { dataIndex: 'deptName', title: '科室名称' },
        { dataIndex: 'currentValue', title: '本期值' },
        { dataIndex: 'contemporaryValue', title: '同期值' },
        { dataIndex: 'growthRate', title: '增长率' },
      ],
      getChartOption: (data) =>
        getBarLineMixChartOption({
          data,
          xAxisField: 'deptName',
          yAxisField: [
            { field: 'currentValue', name: '本期值' },
            { field: 'contemporaryValue', name: '同期值' },
            { field: 'growthRate', name: '增长率' },
          ],
        }),
    },
    prescription: {
      title: `${baseTitle} - 处方类型维度分析`,
      columns: [
        { dataIndex: 'className', title: '处方类型' },
        { dataIndex: 'currentValue', title: '本期值' },
        { dataIndex: 'contemporaryValue', title: '同期值' },
      ],
      getChartOption: (data) =>
        getLinesChartOption({
          data,
          xAxisField: 'className',
          yAxisField: [
            { field: 'currentValue', name: '本期值' },
            { field: 'contemporaryValue', name: '同期值' },
          ],
        }),
    },
    doctor: {
      title: `${baseTitle} - 医生前20名`,
      columns: [
        { dataIndex: 'doctorName', title: '医生名称' },
        { dataIndex: 'currentValue', title: '本期值' },
        { dataIndex: 'contemporaryValue', title: '同期值' },
        { dataIndex: 'growthRate', title: '增长率' },
      ],
      getChartOption: (data) =>
        getBarLineMixChartOption({
          data,
          xAxisField: 'doctorName',
          yAxisField: [
            { field: 'currentValue', name: '本期值' },
            { field: 'contemporaryValue', name: '同期值' },
            { field: 'growthRate', name: '增长率' },
          ],
        }),
    },
  };

  return configs[type];
};
