import { getBarLineMixChartOption, getLinesChartOption } from './data';
import type { ChartConfigMapping } from '/@/views/pages/types';

const activeTabMap = {
  1: {
    prescription: {
      columns: [
        { dataIndex: 'className', title: '处方类型' },
        { dataIndex: 'currentValue', title: '本期值' },
        { dataIndex: 'contemporaryValue', title: '同期值' },
      ],
      xAxisField: 'className',
      yAxisField: [
        { field: 'currentValue', name: '本期值' },
        { field: 'contemporaryValue', name: '同期值' },
      ],
    },
  },
  2: {
    prescription: {
      columns: [
        { dataIndex: 'medicalPaymentType', title: '费用方式' },
        { dataIndex: 'currentValue', title: '本期值' },
        { dataIndex: 'contemporaryValue', title: '同期值' },
      ],
      xAxisField: 'medicalPaymentType',
      yAxisField: [
        { field: 'currentValue', name: '本期值' },
        { field: 'contemporaryValue', name: '同期值' },
      ],
    },
  },
  3: {
    prescription: {
      columns: [
        { dataIndex: 'bedWard', title: '病区' },
        { dataIndex: 'currentValue', title: '本期值' },
        { dataIndex: 'contemporaryValue', title: '同期值' },
      ],
      xAxisField: 'bedWard',
      yAxisField: [
        { field: 'currentValue', name: '本期值' },
        { field: 'contemporaryValue', name: '同期值' },
      ],
    },
  },
  4: {
    prescription: {
      columns: [
        { dataIndex: 'medicalPaymentType', title: '医疗费用方式' },
        { dataIndex: 'currentValue', title: '本期值' },
        { dataIndex: 'contemporaryValue', title: '同期值' },
      ],
      xAxisField: 'medicalPaymentType',
      yAxisField: [
        { field: 'currentValue', name: '本期值' },
        { field: 'contemporaryValue', name: '同期值' },
      ],
    },
  },
};

export const createChartConfig = (
  type: string,
  baseTitle: string,
  activeTabKey?: number,
): ChartConfigMapping => {
  const configs: Record<string, ChartConfigMapping> = {
    trend: {
      title: `${baseTitle}`,
      columns: [
        { dataIndex: 'yearMonth', title: '日期' },
        { dataIndex: 'currentValue', title: '本期值' },
        { dataIndex: 'contemporaryValue', title: '同期值' },
      ],
      getChartOption: (data) =>
        getLinesChartOption({
          data,
          xAxisField: 'yearMonth',
          yAxisField: [
            { field: 'currentValue', name: '本期值' },
            { field: 'contemporaryValue', name: '同期值' },
          ],
        }),
    },
    department: {
      title: `${baseTitle}`,
      columns: [
        { dataIndex: 'deptName', title: '科室名称' },
        { dataIndex: 'currentValue', title: '本期值' },
        { dataIndex: 'contemporaryValue', title: '同期值' },
        { dataIndex: 'growthRate', title: '增长率' },
      ],
      getChartOption: (data) =>
        getBarLineMixChartOption({
          data,
          xAxisField: 'deptName',
          yAxisField: [
            { field: 'currentValue', name: '本期值' },
            { field: 'contemporaryValue', name: '同期值' },
            { field: 'growthRate', name: '增长率' },
          ],
        }),
    },
    prescription: {
      title: `${baseTitle}`,
      columns: activeTabMap[activeTabKey as number]?.prescription?.columns,
      getChartOption: (data) =>
        getLinesChartOption({
          data,
          xAxisField: activeTabMap[activeTabKey as number]?.prescription?.xAxisField,
          yAxisField: activeTabMap[activeTabKey as number]?.prescription?.yAxisField,
        }),
    },
    doctor: {
      title: `${baseTitle}`,
      columns: [
        { dataIndex: 'doctorName', title: '医生名称' },
        { dataIndex: 'currentValue', title: '本期值' },
        { dataIndex: 'contemporaryValue', title: '同期值' },
        { dataIndex: 'growthRate', title: '增长率' },
      ],
      getChartOption: (data) =>
        getBarLineMixChartOption({
          data,
          xAxisField: 'doctorName',
          yAxisField: [
            { field: 'currentValue', name: '本期值' },
            { field: 'contemporaryValue', name: '同期值' },
            { field: 'growthRate', name: '增长率' },
          ],
        }),
    },
  };

  return configs[type];
};
