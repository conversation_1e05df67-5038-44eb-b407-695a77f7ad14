<!-- eslint-disable sort-imports -->
<script lang="ts" setup>
  import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
  import { useMessage } from '@hiip/internal/hooks/web/useMessage';
  import { ChartTable } from '/@/components/ChartTable';
  import { StatCard } from '/@/components/StatCard';
  import { useToggle } from '@vueuse/core';
  import { usePageConfig } from '/@/hooks/usePageConfig';
  import { type ChartDataItem, type TabConfig, ChartType } from '/@/views/pages/types';
  import { exportUtil } from '@hiip/internal/utils';
  import { createChartConfig } from './chart-factory';
  import {
    getCwzzcsCardData,
    getCyhzpjzyrCardData,
    getPjmzcwgzrCardData,
    getCwsylCardData,
    getTrendAnalysisDataForCwsyl,
    exportTrendAnalysisDataForCwsyl,
    getTop20ForDepartmentDataForCwsyl,
    exportTop20ForDepartmentDataForCwsyl,
    getTrendAnalysisDataForCwzzcs,
    exportTrendAnalysisDataForCwzzcs,
    getTop20ForDepartmentDataForCwzzcs,
    exportTop20ForDepartmentDataForCwzzcs,
    getTrendAnalysisDataForPjmzcwgzr,
    exportTrendAnalysisDataForPjmzcwgzr,
    getTop20ForDepartmentDataForPjmzcwgzr,
    exportTop20ForDepartmentDataForPjmzcwgzr,
    getTrendAnalysisDataForCyhzpjzyr,
    exportTrendAnalysisDataForCyhzpjzyr,
    getTop20ForDepartmentDataForCyhzpjzyr,
    exportTop20ForDepartmentDataForCyhzpjzyr,
    getTop20ForDoctorDataForCyhzpjzyr,
    exportTop20ForDoctorDataForCyhzpjzyr,
    getPrescriptionAnalysisDataForCyhzpjzyr,
    exportPrescriptionAnalysisDataForCyhzpjzyr,
    exportTop20ForMedicalPaymentTypeDataForPjmzcwgzr,
    getTop20ForMedicalPaymentTypeDataForPjmzcwgzr,
    exportTop20ForDoctorDataForPjmzcwgzr,
    getTop20ForDoctorDataForPjmzcwgzr,
    exportBedWardAnalysisData,
    exportTop20ForDoctorData,
    exportTop20ForMedicalPaymentTypeData,
    getBedWardAnalysisData,
    getTop20ForDoctorData,
    getTop20ForMedicalPaymentTypeData,
  } from '/@/api/work-efficiency-analysis';
  import { keepDecimalString } from '/@/utils';
  import CommonFilter from '/@/components/CommonFilter/index.vue';

  const { createMessage } = useMessage();
  const { isTabVisible, isChartVisible, getChartColSpan } = usePageConfig();

  const tabMap: TabConfig[] = [
    {
      id: 1,
      title: '出院患者平均住院日（天）',
      key: 'cyhzpjzyr',
      icon: 'mz|svg',
      cardApi: getCyhzpjzyrCardData,
      cardDataKeys: {
        value: 'cyhzpjzyr',
        contemporaryValue: 'cyhzpjzyrContemporaryValue',
        growthRate: 'cyhzpjzyrGrowthRate',
      },
      charts: {
        [ChartType.TREND]: {
          subTitle: '出院患者平均住院日（天）- 趋势分析',
          api: getTrendAnalysisDataForCyhzpjzyr,
          exportApi: exportTrendAnalysisDataForCyhzpjzyr,
        },
        [ChartType.DEPARTMENT]: {
          subTitle: '出院患者平均住院日（天）- 科室前20名',
          api: getTop20ForDepartmentDataForCyhzpjzyr,
          exportApi: exportTop20ForDepartmentDataForCyhzpjzyr,
        },
        [ChartType.PRESCRIPTION]: {
          subTitle: '出院患者平均住院日（天）- 处方类型维度分析',
          api: getPrescriptionAnalysisDataForCyhzpjzyr,
          exportApi: exportPrescriptionAnalysisDataForCyhzpjzyr,
        },
        [ChartType.DOCTOR]: {
          subTitle: '出院患者平均住院日（天）- 医生前20名',
          api: getTop20ForDoctorDataForCyhzpjzyr,
          exportApi: exportTop20ForDoctorDataForCyhzpjzyr,
        },
      },
    },
    {
      id: 2,
      title: '平均每张床位工作日（天）',
      key: 'pjmzcwgzr',
      icon: 'mz|svg',
      cardApi: getPjmzcwgzrCardData,
      cardDataKeys: {
        value: 'pjmzcwgzr',
        contemporaryValue: 'pjmzcwgzrContemporaryValue',
        growthRate: 'pjmzcwgzrGrowthRate',
      },
      charts: {
        [ChartType.TREND]: {
          subTitle: '平均每张床位工作日（天）- 趋势分析',
          api: getTrendAnalysisDataForPjmzcwgzr,
          exportApi: exportTrendAnalysisDataForPjmzcwgzr,
        },
        [ChartType.DEPARTMENT]: {
          subTitle: '平均每张床位工作日（天）- 科室前20名',
          api: getTop20ForDepartmentDataForPjmzcwgzr,
          exportApi: exportTop20ForDepartmentDataForPjmzcwgzr,
        },
        [ChartType.PRESCRIPTION]: {
          subTitle: '平均每张床位工作日（天）- 费用方式',
          api: getTop20ForMedicalPaymentTypeDataForPjmzcwgzr,
          exportApi: exportTop20ForMedicalPaymentTypeDataForPjmzcwgzr,
        },
        [ChartType.DOCTOR]: {
          subTitle: '平均每张床位工作日（天）- 医生前20名',
          api: getTop20ForDoctorDataForPjmzcwgzr,
          exportApi: exportTop20ForDoctorDataForPjmzcwgzr,
        },
      },
    },
    {
      id: 3,
      title: '床位使用率（%）',
      key: 'cwsyl',
      icon: 'mz|svg',
      cardApi: getCwsylCardData,
      cardDataKeys: {
        value: 'cwsyl',
        contemporaryValue: 'cwsylContemporaryValue',
        growthRate: 'cwsylGrowthRate',
      },
      charts: {
        [ChartType.TREND]: {
          subTitle: '床位使用率（%）- 趋势分析',
          api: getTrendAnalysisDataForCwsyl,
          exportApi: exportTrendAnalysisDataForCwsyl,
          colSpan: 24,
        },
        [ChartType.DEPARTMENT]: {
          subTitle: '床位使用率（%）- 科室前20名',
          api: getTop20ForDepartmentDataForCwsyl,
          exportApi: exportTop20ForDepartmentDataForCwsyl,
        },
        [ChartType.PRESCRIPTION]: {
          subTitle: '床位使用率（%）- 维度分析',
          api: getBedWardAnalysisData,
          exportApi: exportBedWardAnalysisData,
        },
      },
    },
    {
      id: 4,
      title: '床位周转次数（%）',
      key: 'cwzzcs',
      icon: 'mz|svg',
      cardApi: getCwzzcsCardData,
      cardDataKeys: {
        value: 'cwzzcs',
        contemporaryValue: 'cwzzcsContemporaryValue',
        growthRate: 'cwzzcsGrowthRate',
      },
      charts: {
        [ChartType.TREND]: {
          subTitle: '床位周转次数（%）- 趋势分析',
          api: getTrendAnalysisDataForCwzzcs,
          exportApi: exportTrendAnalysisDataForCwzzcs,
        },
        [ChartType.DEPARTMENT]: {
          subTitle: '床位周转次数（%）- 科室前20名',
          api: getTop20ForDepartmentDataForCwzzcs,
          exportApi: exportTop20ForDepartmentDataForCwzzcs,
        },
        [ChartType.PRESCRIPTION]: {
          subTitle: '床位周转次数（%）- 费用方式',
          api: getTop20ForMedicalPaymentTypeData,
          exportApi: exportTop20ForMedicalPaymentTypeData,
        },
        [ChartType.DOCTOR]: {
          subTitle: '床位周转次数（%）- 医生前20名',
          api: getTop20ForDoctorData,
          exportApi: exportTop20ForDoctorData,
        },
      },
    },
  ];

  const fetchKey = ref(0);

  // 筛选参数
  const filterParams = ref();
  // 当前选中的 tab
  const activeTab = ref(1);

  // 计算可见的Tabs
  const visibleTabs = computed(() => {
    return tabMap.filter((tab) => isTabVisible({ tabId: tab.id }));
  });

  // 统计数据
  const statsData = reactive<Record<string, string>>({
    /** 出院患者平均住院日 */
    cyhzpjzyr: '0',
    cyhzpjzyrContemporaryValue: '0',
    cyhzpjzyrGrowthRate: '0',

    /** 平均每张床位工作日 */
    pjmzcwgzr: '0',
    pjmzcwgzrContemporaryValue: '0',
    pjmzcwgzrGrowthRate: '0',

    /** 床位使用率 */
    cwsyl: '0',
    cwsylContemporaryValue: '0',
    cwsylGrowthRate: '0',

    /** 床位周转次数 */
    cwzzcs: '0',
    cwzzcsContemporaryValue: '0',
    cwzzcsGrowthRate: '0',
  });

  // 重构图表数据结构
  const chartData = reactive<Record<ChartType, ChartDataItem>>({
    [ChartType.TREND]: {
      data: [],
      loading: false,
      exportLoading: false,
    },
    [ChartType.DEPARTMENT]: {
      data: [],
      loading: false,
      exportLoading: false,
    },
    [ChartType.PRESCRIPTION]: {
      data: [],
      loading: false,
      exportLoading: false,
    },
    [ChartType.DOCTOR]: {
      data: [],
      loading: false,
      exportLoading: false,
    },
  });

  const chartDataLoading = computed(() => {
    return Object.values(chartData).some((state) => state.loading || state.exportLoading);
  });

  const exportLoading = reactive<Record<string, boolean>>({});

  // 统一的 props 构建函数
  const createChartProps = (type: ChartType, baseTitle: string, onDownload?: () => void) => {
    const config = createChartConfig(type, baseTitle, activeTab.value);
    const chartState = chartData[type];
    return {
      title: config.title,
      options: [
        {
          tab: config.title,
          data: chartState.data,
          columns: config.columns,
          chartOption: config.getChartOption(chartState.data),
        },
      ],
      loading: chartState.loading,
      onDownload,
    };
  };

  const handleExport = async (type: ChartType) => {
    const params = filterParams.value;
    const currentTabConfig = tabMap.find((tab) => tab.id === activeTab.value);
    const chartConfig = currentTabConfig?.charts[type];

    if (!chartConfig?.exportApi) {
      createMessage.warning('该图表不支持导出');
      return;
    }

    exportLoading[type] = true;
    try {
      await exportUtil(chartConfig.exportApi(params));
    } catch (error) {
      createMessage.error('导出失败');
      console.error('导出失败:', error);
    } finally {
      exportLoading[type] = false;
    }
  };

  const charts = computed(() => {
    const currentTabConfig = tabMap.find((tab) => tab.id === activeTab.value);

    const chartConfigs = [
      {
        key: ChartType.TREND,
        title: currentTabConfig?.charts[ChartType.TREND]?.subTitle || '',
        colSpan:
          currentTabConfig?.charts[ChartType.TREND]?.colSpan ??
          getChartColSpan({ tabId: activeTab.value, chartType: ChartType.TREND }),
      },
      {
        key: ChartType.DEPARTMENT,
        title: currentTabConfig?.charts[ChartType.DEPARTMENT]?.subTitle || '',
        colSpan: getChartColSpan({ tabId: activeTab.value, chartType: ChartType.DEPARTMENT }),
      },
      {
        key: ChartType.PRESCRIPTION,
        title: currentTabConfig?.charts[ChartType.PRESCRIPTION]?.subTitle || '',
        colSpan: getChartColSpan({ tabId: activeTab.value, chartType: ChartType.PRESCRIPTION }),
      },
      {
        key: ChartType.DOCTOR,
        title: currentTabConfig?.charts[ChartType.DOCTOR]?.subTitle || '',
        colSpan: getChartColSpan({ tabId: activeTab.value, chartType: ChartType.DOCTOR }),
      },
    ];

    return chartConfigs
      .filter(
        (c) =>
          currentTabConfig?.charts[c.key] &&
          isChartVisible({ tabId: activeTab.value, chartType: c.key }),
      )
      .map((chart) => {
        return {
          ...chart,
          downloadLoading: exportLoading[chart.key],
          props: createChartProps(chart.key, chart.title, () => handleExport(chart.key)),
        };
      });
  });

  const [cyhzpjzyrLoading, setCyhzpjzyrLoading] = useToggle(false);
  const [pjmzcwgzrLoading, setPjmzcwgzrLoading] = useToggle(false);
  const [cwsylLoading, setCwsylLoading] = useToggle(false);
  const [cwzzcsLoading, setCwzzcsLoading] = useToggle(false);

  // 刷新 Top Card 数据
  async function getTopCardDataRefresh() {
    const params = filterParams.value;
    const currentFetchKey = fetchKey.value;

    const promises = visibleTabs.value.map(async (tab) => {
      try {
        // 根据 tab.key 设置对应的 loading 状态
        if (tab.key === 'cyhzpjzyr') {
          setCyhzpjzyrLoading(true);
        } else if (tab.key === 'pjmzcwgzr') {
          setPjmzcwgzrLoading(true);
        } else if (tab.key === 'cwsyl') {
          setCwsylLoading(true);
        } else if (tab.key === 'cwzzcs') {
          setCwzzcsLoading(true);
        }

        const result = await tab.cardApi(params);
        if (currentFetchKey === fetchKey.value) {
          // 更新对应的数据
          statsData[tab.cardDataKeys.value] = keepDecimalString(result.value);
          statsData[tab.cardDataKeys.contemporaryValue] = keepDecimalString(
            result.contemporaryValue,
          );
          statsData[tab.cardDataKeys.growthRate] = keepDecimalString(result.growthRate);
        }
      } catch (error) {
        createMessage.error(`获取${tab.title}数据失败`);
        console.error(`获取${tab.title}数据失败:`, error);
      } finally {
        if (tab.key === 'cyhzpjzyr') {
          setCyhzpjzyrLoading(false);
        } else if (tab.key === 'pjmzcwgzr') {
          setPjmzcwgzrLoading(false);
        } else if (tab.key === 'cwsyl') {
          setCwsylLoading(false);
        } else if (tab.key === 'cwzzcs') {
          setCwzzcsLoading(false);
        }
      }
    });

    await Promise.all(promises);
  }

  // 统一的数据刷新函数
  const refreshChartData = async (type: ChartType) => {
    try {
      const currentFetchKey = fetchKey.value;
      const params = filterParams.value;
      const currentTabConfig = tabMap.find((tab) => tab.id === activeTab.value);
      const chartConfig = currentTabConfig?.charts[type];

      if (!chartConfig?.api) {
        chartData[type].data = [];
        return;
      }
      chartData[type].loading = true;
      const result = await chartConfig.api(params);
      if (currentFetchKey === fetchKey.value) {
        chartData[type].data = result;
      }
    } catch (error) {
      createMessage.error('获取图表数据失败');
      chartData[type].data = [];
    } finally {
      chartData[type].loading = false;
    }
  };

  // 刷新所有数据
  const refreshData = async (refreshTopCard = true) => {
    fetchKey.value = new Date().valueOf();
    if (refreshTopCard) {
      getTopCardDataRefresh();
    }

    const currentTabConfig = tabMap.find((tab) => tab.id === activeTab.value);

    if (currentTabConfig) {
      Object.entries(currentTabConfig.charts).forEach(([chartType, chartConfig]) => {
        if (
          chartConfig &&
          isChartVisible({ tabId: activeTab.value, chartType: chartType as ChartType })
        ) {
          refreshChartData(chartType as ChartType);
        }
      });
    }
  };

  watch(activeTab, (newVal, oldVal) => {
    if (newVal !== oldVal) {
      refreshData(false);
    }
  });

  // 重置筛选条件
  const resetFilter = () => {
    nextTick(refreshData);
  };

  // 页面加载时获取数据
  onMounted(() => {
    refreshData();
  });
</script>

<template>
  <div class="h-full flex flex-col gap-2 bg-[#f5f7fa] overflow-hidden">
    <!-- 筛选条件区 -->
    <div class="bg-white p-4 rounded-lg flex items-center space-x-4">
      <slot
        name="filter"
        :loading="
          cyhzpjzyrLoading || pjmzcwgzrLoading || cwsylLoading || cwzzcsLoading || chartDataLoading
        "
      ></slot>
      <CommonFilter v-model="filterParams" @reset="resetFilter" @search="refreshData" />
    </div>

    <!-- 统计卡片区 -->
    <div class="grid grid-cols-4 gap-2">
      <StatCard
        v-for="tab in visibleTabs"
        :key="tab.id"
        :active="activeTab === tab.id"
        :title="tab.title"
        :value="statsData[tab.cardDataKeys.value]"
        :compare-value="statsData[tab.cardDataKeys.contemporaryValue]"
        :rate="statsData[tab.cardDataKeys.growthRate]"
        :icon="tab.icon"
        :loading="
          (tab.key === 'cyhzpjzyr' && cyhzpjzyrLoading) ||
          (tab.key === 'pjmzcwgzr' && pjmzcwgzrLoading) ||
          (tab.key === 'cwsyl' && cwsylLoading) ||
          (tab.key === 'cwzzcs' && cwzzcsLoading)
        "
        @click="activeTab = tab.id"
      />
    </div>

    <!-- 图表区域 -->
    <div class="flex-1 grid grid-cols-24 gap-2 min-h-0 bg-white">
      <div
        v-for="chart in charts"
        :key="chart.key"
        class="p-4 rounded-lg h-[400px]"
        :style="{
          gridColumn: `span ${chart?.colSpan ?? 12} / span ${chart?.colSpan ?? 12}`,
        }"
      >
        <ChartTable v-bind="chart.props" :download-loading="chart.downloadLoading" class="h-full" />
      </div>
    </div>
  </div>
</template>
