<script lang="ts" setup>
  import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
  import { useMessage } from '@hiip/internal/hooks/web/useMessage';
  import { ChartTable } from '/@/components/ChartTable';
  import StatCard from '/@/components/StatCard/StatCard.vue';
  import CommonFilter from '/@/components/CommonFilter/index.vue';
  import { keepDecimalString } from '/@/utils';
  import {
    exportPrescriptionAnalysisData,
    exportPrescriptionAnalysisDataForMzhzkjywcfbl,
    exportPrescriptionAnalysisDataForMzhzzsjcfbl,
    exportPrescriptionAnalysisDataForYzb,
    exportTop20ForDepartmentData,
    exportTop20ForDepartmentDataForMzcfzs,
    exportTop20ForDepartmentDataForMzhzkjywcfbl,
    exportTop20ForDepartmentDataForMzhzzsjcfbl,
    exportTop20ForDoctorData,
    exportTop20ForDoctorDataForMzhzkjywcfbl,
    exportTop20ForDoctorDataForMzhzzsjcfbl,
    exportTop20ForDoctorDataForYzb,
    exportTrendAnalysisData,
    exportTrendAnalysisDataForMzcfzs,
    exportTrendAnalysisDataForMzhzkjywcfbl,
    exportTrendAnalysisDataForMzhzzsjcfbl,
    getMzcfzsCardData,
    getMzhzkjywcfblCardData,
    getMzhzzsjcfblCardData,
    getPrescriptionAnalysisData,
    getPrescriptionAnalysisDataForMzhzkjywcfbl,
    getPrescriptionAnalysisDataForMzhzzsjcfbl,
    getPrescriptionAnalysisDataForYzb,
    getTop20ForDepartmentData,
    getTop20ForDepartmentDataForMzcfzs,
    getTop20ForDepartmentDataForMzhzkjywcfbl,
    getTop20ForDepartmentDataForMzhzzsjcfbl,
    getTop20ForDoctorData,
    getTop20ForDoctorDataForMzhzkjywcfbl,
    getTop20ForDoctorDataForMzhzzsjcfbl,
    getTop20ForDoctorDataForYzb,
    getTrendAnalysisData,
    getTrendAnalysisDataForMzcfzs,
    getTrendAnalysisDataForMzhzkjywcfbl,
    getTrendAnalysisDataForMzhzzsjcfbl,
    getYzbCardData,
  } from '/@/api/reason-drug';
  import { usePageConfig } from '/@/hooks/usePageConfig';
  import { exportUtil } from '@hiip/internal/utils';
  import { ChartType } from '../../types';
  import type { ChartDataItem, TabConfig } from '../../types';
  import { createChartConfig } from './chart-factory';

  const { createMessage } = useMessage();
  const { isTabVisible, isChartVisible, getChartColSpan } = usePageConfig();

  const tabMap: Record<number, TabConfig> = {
    1: {
      id: 1,
      title: '抗菌药物处方数/每百张门诊处方(%)',
      key: 'antibacterialDrugs',
      icon: 'drug|svg',
      cardApi: getMzhzkjywcfblCardData,
      cardDataKeys: {
        value: 'antibacterialDrugs',
        contemporaryValue: 'antibacterialDrugsValue',
        growthRate: 'antibacterialDrugsRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getTrendAnalysisDataForMzhzkjywcfbl,
          exportApi: exportTrendAnalysisDataForMzhzkjywcfbl,
        },
        [ChartType.DEPARTMENT]: {
          api: getTop20ForDepartmentDataForMzhzkjywcfbl,
          exportApi: exportTop20ForDepartmentDataForMzhzkjywcfbl,
        },
        [ChartType.PRESCRIPTION]: {
          api: getPrescriptionAnalysisDataForMzhzzsjcfbl,
          exportApi: exportPrescriptionAnalysisDataForMzhzzsjcfbl,
        },
        [ChartType.DOCTOR]: {
          api: getTop20ForDoctorDataForMzhzzsjcfbl,
          exportApi: exportTop20ForDoctorDataForMzhzzsjcfbl,
        },
      },
    },
    2: {
      id: 2,
      title: '门诊处方总数(张)',
      key: 'outpatient',
      icon: 'prescribe|svg',
      cardApi: getMzcfzsCardData,
      cardDataKeys: {
        value: 'outpatient',
        contemporaryValue: 'outpatientValue',
        growthRate: 'outpatientRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getTrendAnalysisDataForMzcfzs,
          exportApi: exportTrendAnalysisDataForMzcfzs,
        },
        [ChartType.DEPARTMENT]: {
          api: getTop20ForDepartmentDataForMzcfzs,
          exportApi: exportTop20ForDepartmentDataForMzcfzs,
        },
        [ChartType.PRESCRIPTION]: {
          api: getPrescriptionAnalysisData,
          exportApi: exportPrescriptionAnalysisData,
        },
        [ChartType.DOCTOR]: {
          api: getTop20ForDoctorData,
          exportApi: exportTop20ForDoctorData,
        },
      },
    },
    3: {
      id: 3,
      title: '注射剂处方数/每百张门诊处方(%)',
      key: 'injection',
      icon: 'inject|svg',
      cardApi: getMzhzzsjcfblCardData,
      cardDataKeys: {
        value: 'injection',
        contemporaryValue: 'injectionValue',
        growthRate: 'injectionRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getTrendAnalysisDataForMzhzzsjcfbl,
          exportApi: exportTrendAnalysisDataForMzhzzsjcfbl,
        },
        [ChartType.DEPARTMENT]: {
          api: getTop20ForDepartmentDataForMzhzzsjcfbl,
          exportApi: exportTop20ForDepartmentDataForMzhzzsjcfbl,
        },
        [ChartType.PRESCRIPTION]: {
          api: getPrescriptionAnalysisDataForMzhzkjywcfbl,
          exportApi: exportPrescriptionAnalysisDataForMzhzkjywcfbl,
        },
        [ChartType.DOCTOR]: {
          api: getTop20ForDoctorDataForMzhzkjywcfbl,
          exportApi: exportTop20ForDoctorDataForMzhzkjywcfbl,
        },
      },
    },
    4: {
      id: 4,
      title: '药占比(%)',
      key: 'ratio',
      icon: 'ratio|svg',
      cardApi: getYzbCardData,
      cardDataKeys: {
        value: 'ratio',
        contemporaryValue: 'ratioValue',
        growthRate: 'ratioRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getTrendAnalysisData,
          exportApi: exportTrendAnalysisData,
        },
        [ChartType.DEPARTMENT]: {
          api: getTop20ForDepartmentData,
          exportApi: exportTop20ForDepartmentData,
        },
        [ChartType.PRESCRIPTION]: {
          api: getPrescriptionAnalysisDataForYzb,
          exportApi: exportPrescriptionAnalysisDataForYzb,
        },
        [ChartType.DOCTOR]: {
          api: getTop20ForDoctorDataForYzb,
          exportApi: exportTop20ForDoctorDataForYzb,
        },
      },
    },
  };

  // 计算可见的Tabs
  const visibleTabs = computed(() => {
    return Object.values(tabMap).filter((tab) => isTabVisible({ tabId: tab.id }));
  });

  const fetchKey = ref(0);
  const filterParams = ref();
  const activeTab = ref(1);

  const statsData = reactive<Record<string, string>>({
    antibacterialDrugs: '0',
    antibacterialDrugsValue: '0',
    antibacterialDrugsRate: '0',
    outpatient: '0',
    outpatientValue: '0',
    outpatientRate: '0',
    injection: '0',
    injectionValue: '0',
    injectionRate: '0',
    ratio: '0',
    ratioValue: '0',
    ratioRate: '0',
  });

  const cardLoadingStates = reactive<Record<string, boolean>>({
    antibacterialDrugs: false,
    outpatient: false,
    injection: false,
    ratio: false,
  });

  const chartData = reactive<Record<ChartType, ChartDataItem>>({
    [ChartType.TREND]: { data: [], loading: false, exportLoading: false },
    [ChartType.DEPARTMENT]: { data: [], loading: false, exportLoading: false },
    [ChartType.PRESCRIPTION]: { data: [], loading: false, exportLoading: false },
    [ChartType.DOCTOR]: { data: [], loading: false, exportLoading: false },
  });

  const exportLoading = reactive<Record<string, boolean>>({});

  const createChartProps = (type: ChartType, baseTitle: string, onDownload?: () => void) => {
    const config = createChartConfig(type, baseTitle);
    const chartState = chartData[type];

    return {
      title: config.title,
      options: [
        {
          tab: config.title,
          data: chartState.data,
          columns: config.columns,
          chartOption: config.getChartOption(chartState.data, baseTitle),
        },
      ],
      loading: chartState.loading,
      onDownload,
    };
  };

  const handleExport = async (type: ChartType) => {
    const params = filterParams.value;
    const exportFn = tabMap[activeTab.value].charts[type]?.exportApi;

    if (!exportFn) {
      createMessage.warning('该图表不支持导出');
      return;
    }

    exportLoading[type] = true;
    try {
      await exportUtil(exportFn(params));
    } catch (error) {
      createMessage.error('导出失败');
      console.error('导出失败:', error);
    } finally {
      exportLoading[type] = false;
    }
  };

  const charts = computed(() => {
    const activeTabConfig = tabMap[activeTab.value];
    const baseTitle = activeTabConfig.title;

    const chartConfigs = [
      {
        key: ChartType.TREND,
        colSpan: getChartColSpan({ tabId: activeTab.value, chartType: ChartType.TREND }),
      },
      {
        key: ChartType.DEPARTMENT,
        colSpan: getChartColSpan({ tabId: activeTab.value, chartType: ChartType.DEPARTMENT }),
      },
      {
        key: ChartType.PRESCRIPTION,
        colSpan: getChartColSpan({ tabId: activeTab.value, chartType: ChartType.PRESCRIPTION }),
      },
      {
        key: ChartType.DOCTOR,
        colSpan: getChartColSpan({ tabId: activeTab.value, chartType: ChartType.DOCTOR }),
      },
    ];

    return chartConfigs
      .filter(
        (c) =>
          activeTabConfig.charts[c.key] &&
          isChartVisible({ tabId: activeTab.value, chartType: c.key }),
      )
      .map((chart) => {
        return {
          ...chart,
          downloadLoading: exportLoading[chart.key],
          props: createChartProps(chart.key, baseTitle, () => handleExport(chart.key)),
        };
      });
  });

  async function getTopCardDataRefresh() {
    const params = filterParams.value;
    const promises = visibleTabs.value.map(async (tab) => {
      try {
        cardLoadingStates[tab.key] = true;
        const result = await tab.cardApi(params);
        statsData[tab.cardDataKeys.value] = keepDecimalString(result.value);
        statsData[tab.cardDataKeys.contemporaryValue] = keepDecimalString(result.contemporaryValue);
        statsData[tab.cardDataKeys.growthRate] = keepDecimalString(result.growthRate);
      } catch (error) {
        createMessage.error(`获取${tab.title}数据失败`);
        console.error(`获取${tab.title}数据失败:`, error);
      } finally {
        cardLoadingStates[tab.key] = false;
      }
    });
    await Promise.all(promises);
  }

  const refreshChartData = async (type: ChartType, apiCall: () => Promise<any>) => {
    // If there's no API call for this chart type on the current tab, do nothing.
    if (!apiCall) return;
    try {
      const currentFetchKey = fetchKey.value;
      chartData[type].loading = true;
      const result = await apiCall();
      if (currentFetchKey === fetchKey.value) {
        chartData[type].data = result;
      }
    } catch (error) {
      createMessage.error('获取图表数据失败');
      console.error('获取图表数据失败:', error);
    } finally {
      chartData[type].loading = false;
    }
  };

  const refreshData = async (refreshTopCard = true) => {
    if (refreshTopCard) {
      getTopCardDataRefresh();
    }
    fetchKey.value = new Date().valueOf();
    const params = filterParams.value;
    const activeTabConfig = tabMap[activeTab.value];
    const chartApis = activeTabConfig.charts;

    // 清空所有图表数据
    Object.values(chartData).forEach((chart) => {
      chart.data = [];
    });

    if (
      chartApis[ChartType.TREND]?.api &&
      isChartVisible({ tabId: activeTab.value, chartType: ChartType.TREND })
    ) {
      refreshChartData(ChartType.TREND, () => chartApis[ChartType.TREND]!.api(params));
    }
    if (
      chartApis[ChartType.DEPARTMENT]?.api &&
      isChartVisible({ tabId: activeTab.value, chartType: ChartType.DEPARTMENT })
    ) {
      refreshChartData(ChartType.DEPARTMENT, () => chartApis[ChartType.DEPARTMENT]!.api(params));
    }
    if (
      chartApis[ChartType.PRESCRIPTION]?.api &&
      isChartVisible({ tabId: activeTab.value, chartType: ChartType.PRESCRIPTION })
    ) {
      refreshChartData(ChartType.PRESCRIPTION, () =>
        chartApis[ChartType.PRESCRIPTION]!.api(params),
      );
    }
    if (
      chartApis[ChartType.DOCTOR]?.api &&
      isChartVisible({ tabId: activeTab.value, chartType: ChartType.DOCTOR })
    ) {
      refreshChartData(ChartType.DOCTOR, () => chartApis[ChartType.DOCTOR]!.api(params));
    }
  };

  watch(activeTab, () => {
    refreshData(false);
  });

  const resetFilter = () => {
    nextTick(refreshData);
  };

  onMounted(refreshData);
</script>

<template>
  <div
    id="data-view"
    class="h-full overflow-y-auto ml-[-1px] w-[calc(100%+1px)] flex flex-col gap-2 bg-[#F7F8F9] p-2"
  >
    <div class="bg-white p-4 rounded-lg">
      <CommonFilter v-model="filterParams" @search="refreshData(true)" @reset="resetFilter" />
    </div>

    <div class="grid grid-cols-4 gap-2">
      <StatCard
        v-for="tab in visibleTabs"
        :key="tab.id"
        :active="activeTab === tab.id"
        :title="tab.title"
        :value="statsData[tab.cardDataKeys.value]"
        :compare-value="statsData[tab.cardDataKeys.contemporaryValue]"
        :rate="statsData[tab.cardDataKeys.growthRate]"
        :icon="tab.icon"
        :loading="cardLoadingStates[tab.key]"
        @click="activeTab = tab.id"
      />
    </div>

    <div class="flex-1 grid grid-cols-24 gap-2 min-h-0 bg-white p-4 rounded-lg of-hidden">
      <div
        v-for="chart in charts"
        :key="chart.key"
        class="rounded-lg h-[400px]"
        :style="{ gridColumn: `span ${chart?.colSpan ?? 1} / span ${chart?.colSpan ?? 1}` }"
      >
        <ChartTable v-bind="chart.props" :download-loading="chart.downloadLoading" class="h-full" />
      </div>
    </div>
  </div>
</template>
