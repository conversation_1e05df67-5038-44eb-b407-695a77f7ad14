<script setup lang="ts">
  import { computed, nextTick, onMounted, reactive, ref } from 'vue';
  import { Icon } from '@hiip/internal/components/Icon';
  import { ChartTable } from '/@/components/ChartTable';
  import { debounce } from 'lodash-es';
  import CommonFilter from '/@/components/CommonFilter/index.vue';
  import { useMessage } from '@hiip/internal/hooks/web/useMessage';
  import { useRequest } from '@hiip/internal/utils/http/useRequest';
  import { exportUtil } from '@hiip/internal/utils';
  import { getBarLineMixChartOption, getTrendChartOption, getTypeChartOption } from './data';
  import {
    exportGsasfjmzhzzsAgeGroupData,
    exportGsasfjmzhzzsTop20ForDepartmentData,
    exportGsasfjmzhzzsTop20ForDoctorData,
    exportGsasfjmzhzzsTrendAnalysisData,
    exportJzsykjywbflAgeGroupData,
    exportJzsykjywbflRegistrationTypeData,
    exportJzsykjywbflTop20ForDepartmentData,
    exportJzsykjywbflTop20ForDoctorData,
    exportJzsykjywbflTrendAnalysisData,
    exportJzsyzsywbflAgeGroupData,
    exportJzsyzsywbflRegistrationTypeData,
    exportJzsyzsywbflTop20ForDepartmentData,
    exportJzsyzsywbflTop20ForDoctorData,
    exportJzsyzsywbflTrendAnalysisData,
    exportKjywcflRegistrationTypeData,
    exportKjywcflTop20ForAgeGroupData,
    exportKjywcflTop20ForDepartmentData,
    exportKjywcflTop20ForDoctorData,
    exportKjywcflTrendAnalysisData,
    exportKjywzxyckjebzTop20ForDepartmentData,
    exportKjywzxyckjebzTrendAnalysisData,
    exportMzksh24xsnswhzsAgeGroupData,
    exportMzksh24xsnswhzsTop20ForDepartmentData,
    exportMzksh24xsnswhzsTop20ForDoctorData,
    exportMzksh24xsnswhzsTrendAnalysisData,
    exportMzzlsAgeGroupData,
    exportMzzlsTop20ForDepartmentData,
    exportMzzlsTop20ForDoctorData,
    exportMzzlsTrendAnalysisData,
    exportSsswrsAgeGroupData,
    exportSsswrsTop20ForDepartmentData,
    exportSsswrsTop20ForDoctorData,
    exportSsswrsTrendAnalysisData,
    exportZyhzrjsykjywfyAgeGroupData,
    exportZyhzrjsykjywfyTop20ForDepartmentData,
    exportZyhzrjsykjywfyTop20ForDoctorData,
    exportZyhzrjsykjywfyTrendAnalysisData,
    getGsasfjmzhzzsAgeGroupData,
    getGsasfjmzhzzsCardData,
    getGsasfjmzhzzsTop20ForDepartmentData,
    getGsasfjmzhzzsTop20ForDoctorData,
    getGsasfjmzhzzsTrendAnalysisData,
    getJzsykjywbflAgeGroupData,
    getJzsykjywbflCardData,
    getJzsykjywbflRegistrationTypeData,
    getJzsykjywbflTop20ForDepartmentData,
    getJzsykjywbflTop20ForDoctorData,
    getJzsykjywbflTrendAnalysisData,
    getJzsyzsywbflAgeGroupData,
    getJzsyzsywbflCardData,
    getJzsyzsywbflRegistrationTypeData,
    getJzsyzsywbflTop20ForDepartmentData,
    getJzsyzsywbflTop20ForDoctorData,
    getJzsyzsywbflTrendAnalysisData,
    getKjywcflCardData,
    getKjywcflRegistrationTypeData,
    getKjywcflTop20ForAgeGroupData,
    getKjywcflTop20ForDepartmentData,
    getKjywcflTop20ForDoctorData,
    getKjywcflTrendAnalysisData,
    getKjywzxyckjebzTop20ForDepartmentData,
    getKjywzxyckjebzTrendAnalysisData,
    getKjywzxyckzjebzCardData,
    getMzksh24xsnswhzsAgeGroupData,
    getMzksh24xsnswhzsCardData,
    getMzksh24xsnswhzsTop20ForDepartmentData,
    getMzksh24xsnswhzsTop20ForDoctorData,
    getMzksh24xsnswhzsTrendAnalysisData,
    getMzzlsAgeGroupData,
    getMzzlsCardData,
    getMzzlsTop20ForDepartmentData,
    getMzzlsTop20ForDoctorData,
    getMzzlsTrendAnalysisData,
    getSsswrsAgeGroupData,
    getSsswrsCardData,
    getSsswrsTop20ForDepartmentData,
    getSsswrsTop20ForDoctorData,
    getSsswrsTrendAnalysisData,
    getZyhzrjsykjywfyAgeGroupData,
    getZyhzrjsykjywfyCardData,
    getZyhzrjsykjywfyTop20ForDepartmentData,
    getZyhzrjsykjywfyTop20ForDoctorData,
    getZyhzrjsykjywfyTrendAnalysisData,
  } from '/@/api/medical';

  // Constants and Types
  interface ChartConfig {
    key: string;
    label: string;
    icon: string;
    cardApi: (params: any) => Promise<any>;
    charts: Record<
      string,
      {
        api: (params: any) => Promise<any>;
        export: (params: any) => Promise<any>;
        exportLoading: boolean;
      }
    >;
  }

  interface ChartDataState {
    loading: boolean;
    trendColumns: Array<{ dataIndex: string; title: string }>;
    trendData: any[];
    departmentColumns: Array<{ dataIndex: string; title: string }>;
    departmentData: any[];
    typeColumns: Array<{ dataIndex: string; title: string }>;
    typeData: any[];
    doctorData: any[];
    ageData: any[];
    doctorColumns: Array<{ dataIndex: string; title: string }>;
    ageColumns: Array<{ dataIndex: string; title: string }>;
  }

  const CARD_CONFIGS: ChartConfig[] = [
    {
      key: '1',
      label: '就诊使用抗菌药物百分率（%）',
      icon: 'drug|svg',
      cardApi: getJzsykjywbflCardData,
      charts: {
        趋势: {
          api: getJzsykjywbflTrendAnalysisData,
          export: exportJzsykjywbflTrendAnalysisData,
          exportLoading: false,
        },
        科室: {
          api: getJzsykjywbflTop20ForDepartmentData,
          export: exportJzsykjywbflTop20ForDepartmentData,
          exportLoading: false,
        },
        医生: {
          api: getJzsykjywbflTop20ForDoctorData,
          export: exportJzsykjywbflTop20ForDoctorData,
          exportLoading: false,
        },
        年龄段: {
          api: getJzsykjywbflAgeGroupData,
          export: exportJzsykjywbflAgeGroupData,
          exportLoading: false,
        },
        挂号类别: {
          api: getJzsykjywbflRegistrationTypeData,
          export: exportJzsykjywbflRegistrationTypeData,
          exportLoading: false,
        },
      },
    },
    {
      key: '2',
      label: '就诊使用注射药物百分率（%）',
      icon: 'prescribe|svg',
      cardApi: getJzsyzsywbflCardData,
      charts: {
        趋势: {
          api: getJzsyzsywbflTrendAnalysisData,
          export: exportJzsyzsywbflTrendAnalysisData,
          exportLoading: false,
        },
        科室: {
          api: getJzsyzsywbflTop20ForDepartmentData,
          export: exportJzsyzsywbflTop20ForDepartmentData,
          exportLoading: false,
        },
        医生: {
          api: getJzsyzsywbflTop20ForDoctorData,
          export: exportJzsyzsywbflTop20ForDoctorData,
          exportLoading: false,
        },
        年龄段: {
          api: getJzsyzsywbflAgeGroupData,
          export: exportJzsyzsywbflAgeGroupData,
          exportLoading: false,
        },
        挂号类别: {
          api: getJzsyzsywbflRegistrationTypeData,
          export: exportJzsyzsywbflRegistrationTypeData,
          exportLoading: false,
        },
      },
    },
    {
      key: '3',
      label: '手术患者并发症发病率（%）',
      icon: 'inject|svg',
      cardApi: getJzsyzsywbflCardData,
      charts: {},
    },
    {
      key: '4',
      label: '住院患者人均使用抗菌药物费用（元）',
      icon: 'ratio|svg',
      cardApi: getZyhzrjsykjywfyCardData,
      charts: {
        趋势: {
          api: getZyhzrjsykjywfyTrendAnalysisData,
          export: exportZyhzrjsykjywfyTrendAnalysisData,
          exportLoading: false,
        },
        科室: {
          api: getZyhzrjsykjywfyTop20ForDepartmentData,
          export: exportZyhzrjsykjywfyTop20ForDepartmentData,
          exportLoading: false,
        },
        医生: {
          api: getZyhzrjsykjywfyTop20ForDoctorData,
          export: exportZyhzrjsykjywfyTop20ForDoctorData,
          exportLoading: false,
        },
        年龄段: {
          api: getZyhzrjsykjywfyAgeGroupData,
          export: exportZyhzrjsykjywfyAgeGroupData,
          exportLoading: false,
        },
      },
    },
    {
      key: '5',
      label: '麻醉开始后24小时内死亡患者数（人）',
      icon: 'proportion|svg',
      cardApi: getMzksh24xsnswhzsCardData,
      charts: {
        趋势: {
          api: getMzksh24xsnswhzsTrendAnalysisData,
          export: exportMzksh24xsnswhzsTrendAnalysisData,
          exportLoading: false,
        },
        科室: {
          api: getMzksh24xsnswhzsTop20ForDepartmentData,
          export: exportMzksh24xsnswhzsTop20ForDepartmentData,
          exportLoading: false,
        },
        医生: {
          api: getMzksh24xsnswhzsTop20ForDoctorData,
          export: exportMzksh24xsnswhzsTop20ForDoctorData,
          exportLoading: false,
        },
        年龄段: {
          api: getMzksh24xsnswhzsAgeGroupData,
          export: exportMzksh24xsnswhzsAgeGroupData,
          exportLoading: false,
        },
      },
    },
    {
      key: '6',
      label: '各ASA分级麻醉患者总数（人）',
      icon: 'proportion|svg',
      cardApi: getGsasfjmzhzzsCardData,
      charts: {
        趋势: {
          api: getGsasfjmzhzzsTrendAnalysisData,
          export: exportGsasfjmzhzzsTrendAnalysisData,
          exportLoading: false,
        },
        科室: {
          api: getGsasfjmzhzzsTop20ForDepartmentData,
          export: exportGsasfjmzhzzsTop20ForDepartmentData,
          exportLoading: false,
        },
        医生: {
          api: getGsasfjmzhzzsTop20ForDoctorData,
          export: exportGsasfjmzhzzsTop20ForDoctorData,
          exportLoading: false,
        },
        年龄段: {
          api: getGsasfjmzhzzsAgeGroupData,
          export: exportGsasfjmzhzzsAgeGroupData,
          exportLoading: false,
        },
      },
    },
    {
      key: '7',
      label: '抗菌药物处方数/每百张门诊处方（%）',
      icon: 'proportion|svg',
      cardApi: getKjywcflCardData,
      charts: {
        趋势: {
          api: getKjywcflTrendAnalysisData,
          export: exportKjywcflTrendAnalysisData,
          exportLoading: false,
        },
        科室: {
          api: getKjywcflTop20ForDepartmentData,
          export: exportKjywcflTop20ForDepartmentData,
          exportLoading: false,
        },
        医生: {
          api: getKjywcflTop20ForDoctorData,
          export: exportKjywcflTop20ForDoctorData,
          exportLoading: false,
        },
        年龄段: {
          api: getKjywcflTop20ForAgeGroupData,
          export: exportKjywcflTop20ForAgeGroupData,
          exportLoading: false,
        },
        挂号类别: {
          api: getKjywcflRegistrationTypeData,
          export: exportKjywcflRegistrationTypeData,
          exportLoading: false,
        },
      },
    },
    {
      key: '8',
      label: '抗菌药物占西药出库总金额比重（%）',
      icon: 'proportion|svg',
      cardApi: getKjywzxyckzjebzCardData,
      charts: {
        趋势: {
          api: getKjywzxyckjebzTrendAnalysisData,
          export: exportKjywzxyckjebzTrendAnalysisData,
          exportLoading: false,
        },
        科室: {
          api: getKjywzxyckjebzTop20ForDepartmentData,
          export: exportKjywzxyckjebzTop20ForDepartmentData,
          exportLoading: false,
        },
      },
    },
    {
      key: '9',
      label: '死亡人数(手术)（人）',
      icon: 'proportion|svg',
      cardApi: getSsswrsCardData,
      charts: {
        趋势: {
          api: getSsswrsTrendAnalysisData,
          export: exportSsswrsTrendAnalysisData,
          exportLoading: false,
        },
        科室: {
          api: getSsswrsTop20ForDepartmentData,
          export: exportSsswrsTop20ForDepartmentData,
          exportLoading: false,
        },
        医生: {
          api: getSsswrsTop20ForDoctorData,
          export: exportSsswrsTop20ForDoctorData,
          exportLoading: false,
        },
        年龄段: {
          api: getSsswrsAgeGroupData,
          export: exportSsswrsAgeGroupData,
          exportLoading: false,
        },
      },
    },
    {
      key: '10',
      label: '麻醉总例数（例）',
      icon: 'proportion|svg',
      cardApi: getMzzlsCardData,
      charts: {
        趋势: {
          api: getMzzlsTrendAnalysisData,
          export: exportMzzlsTrendAnalysisData,
          exportLoading: false,
        },
        科室: {
          api: getMzzlsTop20ForDepartmentData,
          export: exportMzzlsTop20ForDepartmentData,
          exportLoading: false,
        },
        医生: {
          api: getMzzlsTop20ForDoctorData,
          export: exportMzzlsTop20ForDoctorData,
          exportLoading: false,
        },
        年龄段: {
          api: getMzzlsAgeGroupData,
          export: exportMzzlsAgeGroupData,
          exportLoading: false,
        },
      },
    },
    // ... other card configs (simplified for example)
  ];

  // Component Logic
  const { createMessage } = useMessage();
  const filterParams = ref();
  const activeItem = ref<any>(CARD_CONFIGS[0]);
  const exportLoading = reactive<Record<string, boolean>>({
    趋势: false,
    科室: false,
    医生: false,
    年龄段: false,
    挂号类别: false,
  });

  const chartData = reactive<ChartDataState>({
    loading: false,
    trendColumns: [
      { dataIndex: 'yearMonth', title: '日期' },
      { dataIndex: 'currentValue', title: '本期值' },
      { dataIndex: 'contemporaryValue', title: '同期值' },
    ],
    trendData: [],
    departmentColumns: [
      { dataIndex: 'deptName', title: '科室' },
      { dataIndex: 'currentValue', title: '本期值' },
      { dataIndex: 'contemporaryValue', title: '同期值' },
      { dataIndex: 'growthRate', title: '增长率' },
    ],
    doctorColumns: [
      { dataIndex: 'doctorName', title: '医生姓名' },
      { dataIndex: 'currentValue', title: '本期值' },
      { dataIndex: 'contemporaryValue', title: '同期值' },
      { dataIndex: 'growthRate', title: '增长率' },
    ],
    departmentData: [],
    typeColumns: [
      { dataIndex: 'registrationType', title: '挂号类别' },
      { dataIndex: 'contemporaryValue', title: '同期值' },
      { dataIndex: 'currentValue', title: '本期值' },
    ],
    typeData: [],
    doctorData: [],
    ageData: [],
    ageColumns: [
      { dataIndex: 'ageGroup', title: '年龄段' },
      { dataIndex: 'contemporaryValue', title: '同期值' },
      { dataIndex: 'currentValue', title: '本期值' },
    ],
  });

  // Initialize card data requests
  const cardData = reactive(
    CARD_CONFIGS.reduce((acc, config) => {
      acc[config.key] = useRequest(config.cardApi, { manual: true });
      return acc;
    }, {} as Record<string, ReturnType<typeof useRequest>>),
  );

  // Computed
  const leftDataList = computed(() => {
    return CARD_CONFIGS.map((config) => {
      const data: any = cardData[config.key].data || {};
      return {
        key: config.key,
        label: config.label,
        icon: config.icon,
        value: data.value || 0,
        synchronismValue: data?.contemporaryValue || 0,
        previousValue: data?.growthRate || 0,
      };
    });
  });

  // Methods
  const resetFilter = () => {
    nextTick(refreshData);
  };

  const fetchChartData = async (apiFunc: (params: any) => Promise<any>, params: any) => {
    try {
      return (await apiFunc(params)) || [];
    } catch (error) {
      console.error('获取数据失败:', error);
      return [];
    }
  };

  const getChartData = debounce(async () => {
    try {
      chartData.loading = true;
      const activeKey = activeItem.value.key;
      const config = CARD_CONFIGS.find((c) => c.key === activeKey)?.charts || {};
      const params = { ...filterParams.value };

      const fetchPromises: any = [];

      if (config.趋势)
        fetchPromises.push(
          fetchChartData(config.趋势.api, params).then((data) => (chartData.trendData = data)),
        );
      if (config.科室)
        fetchPromises.push(
          fetchChartData(config.科室.api, params).then((data) => (chartData.departmentData = data)),
        );
      if (config.医生)
        fetchPromises.push(
          fetchChartData(config.医生.api, params).then((data) => (chartData.doctorData = data)),
        );
      if (config.年龄段)
        fetchPromises.push(
          fetchChartData(config.年龄段.api, params).then((data) => (chartData.ageData = data)),
        );
      if (config.挂号类别)
        fetchPromises.push(
          fetchChartData(config.挂号类别.api, params).then((data) => (chartData.typeData = data)),
        );

      await Promise.all(fetchPromises);
    } catch (error) {
      createMessage.error('获取图表数据失败');
      console.error('获取图表数据失败:', error);
    } finally {
      chartData.loading = false;
    }
  }, 500);

  const refreshData = debounce(async () => {
    try {
      chartData.loading = true;
      const params = { ...filterParams.value };

      const cardPromises = CARD_CONFIGS.map((config) => cardData[config.key]?.runAsync(params));
      await Promise.all([...cardPromises, getChartData()]);
    } catch (error) {
      createMessage.error('获取数据失败');
      console.error('获取数据失败:', error);
    } finally {
      chartData.loading = false;
    }
  }, 500);

  const handleTabClick = async (item: (typeof leftDataList.value)[number]) => {
    activeItem.value = item;
    await getChartData();
  };

  const handleExport = async (type: string) => {
    const activeConfig = CARD_CONFIGS.find((c) => c.key === activeItem.value.key);
    if (!activeConfig?.charts?.[type]?.export) {
      createMessage.warning('暂不支持导出此类型数据');
      return;
    }

    exportLoading[type] = true;
    try {
      await exportUtil(activeConfig.charts[type].export(filterParams.value));
    } catch (error) {
      createMessage.error('导出失败');
      console.error('导出失败:', error);
    } finally {
      exportLoading[type] = false;
    }
  };

  // Lifecycle
  onMounted(() => {
    refreshData();
  });
</script>

<template>
  <div class="h-full flex flex-col gap-2.5">
    <div class="bg-white p-4 rounded-lg">
      <CommonFilter v-model="filterParams" @search="refreshData()" @reset="resetFilter" />
    </div>
    <div class="flex-1 flex gap-2">
      <!-- Left Cards -->
      <div class="w-680px rounded-lg of-hidden">
        <div class="h-full flex flex-col">
          <div class="grid grid-cols-2 auto-rows-min gap-10px of-y-auto min-h-0 flex-1 basis-0">
            <div
              v-for="item in leftDataList"
              :key="item.key"
              class="h-[128px] flex gap-10px bg-white p-3 rounded-2 transition-all duration-300"
              :class="{ 'card-active': activeItem?.key === item.key }"
              @click="handleTabClick(item)"
            >
              <Icon class="min-w-[29px]" :size="29" :icon="item.icon" />
              <div class="flex flex-col flex-1">
                <div class="flex items-center">
                  <div
                    class="text text-#333 text-14px leading-7 fw-500 truncate"
                    :title="item.label"
                  >
                    {{ item.label }}
                  </div>
                  <Icon color="#999999" icon="ant-design:question-circle-outlined" />
                </div>
                <div class="text text-#1A73E8 text-28px leading-8 fw-500">{{ item.value }}</div>
                <div class="flex pt-4 gap-6">
                  <div class="flex gap-1">
                    <div class="text text-#999 text-14px leading-22px truncate" title="同期"
                      >同期</div
                    >
                    <div class="text text-#333 text-18px leading-22px fw-500">
                      {{ item.synchronismValue }}
                    </div>
                  </div>
                  <div class="flex gap-1">
                    <div class="text text-#999 text-14px leading-22px truncate" title="上期"
                      >上期</div
                    >
                    <div class="text text-#333 text-18px leading-22px fw-500">
                      {{ item.previousValue }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Charts -->
      <div class="flex-1 grid grid-rows-3 gap-2.5 of-hidden">
        <div class="bg-white p-4 rounded-3 h-314px of-hidden">
          <ChartTable
            :title="`${activeItem?.label}-趋势图`"
            :options="[
              {
                tab: '趋势',
                data: chartData.trendData,
                columns: chartData.trendColumns,
                chartOption: getTrendChartOption({
                  data: chartData.trendData,
                  xAxisField: 'yearMonth',
                  yAxisField: [
                    { field: 'currentValue', name: '本期值' },
                    { field: 'contemporaryValue', name: '同期值' },
                  ],
                }),
              },
            ]"
            :loading="chartData.loading"
            @download="(type) => handleExport(type!)"
            :downloadLoading="exportLoading['趋势']"
            class="h-full"
          />
        </div>
        <div class="bg-white p-4 rounded-3 h-314px of-hidden">
          <ChartTable
            :title="`${activeItem?.label}-排名TOP10`"
            @download="(type) => handleExport(type!)"
            :downloadLoading="exportLoading['科室']"
            :options="[
              {
                tab: '科室',
                data: chartData.departmentData,
                columns: chartData.departmentColumns,
                chartOption: getBarLineMixChartOption({
                  data: chartData.departmentData,
                  xAxisField: 'deptName',
                  yAxisField: [
                    { field: 'currentValue', name: '本期值' },
                    { field: 'contemporaryValue', name: '同期值' },
                    { field: 'growthRate', name: '增长率' },
                  ],
                }),
              },
              {
                tab: '医生',
                data: chartData.doctorData,
                columns: chartData.doctorColumns,
                chartOption: getBarLineMixChartOption({
                  data: chartData.doctorData,
                  xAxisField: 'doctorName',
                  yAxisField: [
                    { field: 'currentValue', name: '本期值' },
                    { field: 'contemporaryValue', name: '同期值' },
                    { field: 'growthRate', name: '增长率' },
                  ],
                }),
              },
            ]"
            class="h-full"
          />
        </div>

        <div class="bg-white p-4 rounded-3 h-314px of-hidden">
          <ChartTable
            :title="`${activeItem?.label}-纬度分析`"
            @download="(type) => handleExport(type!)"
            :downloadLoading="exportLoading['年龄段']"
            :options="[
              {
                tab: '年龄段',
                data: chartData.ageData,
                columns: chartData.ageColumns,
                chartOption: getTypeChartOption({
                  data: chartData.ageData,
                  xAxisField: 'ageGroup',
                  yAxisField: [
                    { field: 'contemporaryValue', name: '同期值' },
                    { field: 'currentValue', name: '本期值' },
                  ],
                }),
              },
              {
                tab: '挂号类别',
                data: chartData.typeData,
                columns: chartData.typeColumns,
                chartOption: getTypeChartOption({
                  data: chartData.typeData,
                  xAxisField: 'registrationType',
                  yAxisField: [
                    { field: 'currentValue', name: '本期值' },
                    { field: 'contemporaryValue', name: '同期值' },
                  ],
                }),
              },
            ]"
            :loading="chartData.loading"
            class="h-full"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .card-active {
    background: #d8eaff !important;
    border: 1px solid #2980f6 !important;
  }
</style>
