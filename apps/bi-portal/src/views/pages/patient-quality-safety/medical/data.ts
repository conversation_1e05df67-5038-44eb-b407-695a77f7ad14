import type { EChartsOption } from '/@@/hooks/web/useECharts';
import type { ChartConfig } from '/@/components/ChartTable';
import { createBarChart, createBarLineMixChart, createLineChart } from '/@/components/ChartTable';
import { ellipsisText } from '/@/utils';

interface GenChartDataProps extends ChartConfig {
  data: Recordable[];
}

/** 趋势图配置 */
export function getTrendChartOption(opt: GenChartDataProps): EChartsOption {
  return createLineChart(opt, {
    tooltip: {
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '10%',
    },
  });
}

// 排名TOP10分析图配置
export const getRankChartOption = (opt: GenChartDataProps): EChartsOption => {
  return createBarChart(opt, {
    grid: {
      top: '10%',
      left: '8%',
      right: '8%',
      bottom: '10%',
    },
  });
};

// 纬度分析图配置
export const getTypeChartOption = (opt: GenChartDataProps): EChartsOption => {
  return createBarChart(opt, {
    grid: {
      top: '10%',
      left: '8%',
      right: '8%',
      bottom: '10%',
    },
  });
};

/** 柱线混合图配置 */
export const getBarLineMixChartOption = (opt: ChartConfig, dataZoom = false): EChartsOption => {
  const options: Partial<EChartsOption> = {
    yAxis: [
      {},
      {
        axisLabel: {
          formatter: '{value}%',
        },
        splitLine: {
          show: false,
        },
      },
    ],
    legend: {
      data: ['本期值', '同期值', '增长率'],
    },
    xAxis: [
      {
        axisLabel: {
          rotate: 45,
          formatter: (value: string) => ellipsisText(value, 3),
          fontSize: 12,
        },
      },
    ],
    grid: [
      {
        bottom: 0,
      },
    ],
  };
  // 如果需要数据缩放，添加相应配置
  if (dataZoom) {
    options.dataZoom = {
      show: true,
      realtime: true,
      start: 0,
      end: 50,
      xAxisIndex: [0, 1],
    };
  }

  return createBarLineMixChart(opt, options);
};
