export interface ChartBaseConfig {
  title: string;
  data: any[];
  columns: { dataIndex: string; title: string }[];
  loading: boolean;
  exportLoading?: boolean;
  onDownload?: () => void;
}

export enum ChartType {
  TREND = 'trend',
  DEPARTMENT = 'department',
  PRESCRIPTION = 'prescription',
  DOCTOR = 'doctor',
}

export interface ChartDataItem {
  data: any[];
  loading: boolean;
  exportLoading: boolean;
}

export interface ChartConfigMapping {
  title: string;
  columns: { dataIndex: string; title: string }[];
  getChartOption: (data: any[], tabTitle?: string) => any;
}

export interface TabConfig {
  id: number;
  title: string;
  key: string;
  icon: string;
  cardApi: (params: any) => Promise<any>;
  cardDataKeys: {
    value: string;
    contemporaryValue: string;
    growthRate: string;
  };
  charts: Partial<
    Record<
      ChartType,
      {
        subTitle?: string;
        colSpan?: number;
        api: (params: any) => Promise<any>;
        exportApi?: (params: any) => Promise<any>;
      }
    >
  >;
  layout?: Partial<Record<ChartType, { colSpan: number }>>;
}

export interface WMTabConfig extends TabConfig {
  type: '1' | '2';
}
