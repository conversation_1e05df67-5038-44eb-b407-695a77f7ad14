<script lang="ts" setup>
  import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
  import { useMessage } from '@hiip/internal/hooks/web/useMessage';
  import { ChartTable } from '/@/components/ChartTable';
  import { StatCard } from '/@/components/StatCard';
  import { useToggle } from '@vueuse/core';
  import { usePageConfig } from '/@/hooks/usePageConfig';
  import { ChartType } from '/@/views/pages/types';
  import type { ChartDataItem, TabConfig } from '/@/views/pages/types';
  import { exportUtil } from '@hiip/internal/utils';
  import { createChartConfig } from './chart-factory';
  import CommonFilter from '/@/components/CommonFilter/index.vue';
  import {
    exportInpatientPerPersonPrescriptionAnalysisData,
    exportInpatientPerPersonTop20ForDepartmentData,
    exportInpatientPerPersonTop20ForDoctorData,
    exportInpatientPerPersonTrendAnalysisData,
    exportInpatientTop20ForDepartmentData,
    exportInpatientTrendAnalysisData,
    exportInpatientZycjyfPrescriptionAnalysisData,
    exportInpatientZycjyfTop20ForDoctorData,
    getInpatientPerPersonPrescriptionAnalysisData,
    getInpatientPerPersonTop20ForDepartmentData,
    getInpatientPerPersonTop20ForDoctorData,
    getInpatientPerPersonTrendAnalysisData,
    getInpatientTop20ForDepartmentData,
    getInpatientTrendAnalysisData,
    getInpatientZycjyfCardData,
    getInpatientZycjyfPrescriptionAnalysisData,
    getInpatientZycjyfTop20ForDoctorData,
    getInpatientZyrjfyCardData,
  } from '/@/api/patient-burden-analysis';
  import { keepDecimalString } from '/@/utils';

  const { createMessage } = useMessage();
  const { isTabVisible, isChartVisible, getChartColSpan } = usePageConfig();

  const tabMap: TabConfig[] = [
    {
      id: 1,
      title: '住院人均费用（元）',
      key: 'perPerson',
      icon: 'mz|svg',
      cardApi: getInpatientZyrjfyCardData,
      cardDataKeys: {
        value: 'perPerson',
        contemporaryValue: 'perPersonContemporaryValue',
        growthRate: 'perPersonRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getInpatientPerPersonTrendAnalysisData,
          exportApi: exportInpatientPerPersonTrendAnalysisData,
        },
        [ChartType.DEPARTMENT]: {
          api: getInpatientPerPersonTop20ForDepartmentData,
          exportApi: exportInpatientPerPersonTop20ForDepartmentData,
        },
        [ChartType.PRESCRIPTION]: {
          api: getInpatientPerPersonPrescriptionAnalysisData,
          exportApi: exportInpatientPerPersonPrescriptionAnalysisData,
        },
        [ChartType.DOCTOR]: {
          api: getInpatientPerPersonTop20ForDoctorData,
          exportApi: exportInpatientPerPersonTop20ForDoctorData,
        },
      },
    },
    {
      id: 2,
      title: '住院次均费用（含急诊、体检）（元）',
      key: 'average',
      icon: 'mz|svg',
      cardApi: getInpatientZycjyfCardData,
      cardDataKeys: {
        value: 'average',
        contemporaryValue: 'averageContemporaryValue',
        growthRate: 'averageRate',
      },
      charts: {
        [ChartType.TREND]: {
          api: getInpatientTrendAnalysisData,
          exportApi: exportInpatientTrendAnalysisData,
        },
        [ChartType.DEPARTMENT]: {
          api: getInpatientTop20ForDepartmentData,
          exportApi: exportInpatientTop20ForDepartmentData,
        },
        [ChartType.PRESCRIPTION]: {
          api: getInpatientZycjyfPrescriptionAnalysisData,
          exportApi: exportInpatientZycjyfPrescriptionAnalysisData,
        },
        [ChartType.DOCTOR]: {
          api: getInpatientZycjyfTop20ForDoctorData,
          exportApi: exportInpatientZycjyfTop20ForDoctorData,
        },
      },
    },
  ];

  const fetchKey = ref(0);

  // 筛选参数
  const filterParams = ref();
  // 当前选中的tab
  const activeTab = ref(1);

  // 计算可见的Tabs
  const visibleTabs = computed(() => {
    return tabMap.filter((tab) => isTabVisible({ tabId: tab.id }));
  });

  // 统计数据
  const statsData = reactive<Record<string, string>>({
    /** 人均 */
    perPerson: '0',
    perPersonContemporaryValue: '0',
    perPersonRate: '0',

    /** 次均 */
    average: '0',
    averageContemporaryValue: '0',
    averageRate: '0',
  });

  // 重构图表数据结构
  const chartData = reactive<Record<ChartType, ChartDataItem>>({
    [ChartType.TREND]: {
      data: [],
      loading: false,
      exportLoading: false,
    },
    [ChartType.DEPARTMENT]: {
      data: [],
      loading: false,
      exportLoading: false,
    },
    [ChartType.PRESCRIPTION]: {
      data: [],
      loading: false,
      exportLoading: false,
    },
    [ChartType.DOCTOR]: {
      data: [],
      loading: false,
      exportLoading: false,
    },
  });

  const exportLoading = reactive<Record<string, boolean>>({});

  // 统一的 props 构建函数
  const createChartProps = (type: ChartType, baseTitle: string, onDownload?: () => void) => {
    const config = createChartConfig(type, baseTitle);
    const chartState = chartData[type];

    return {
      title: config.title,
      options: [
        {
          tab: config.title,
          data: chartState.data,
          columns: config.columns,
          chartOption: config.getChartOption(chartState.data),
        },
      ],
      loading: chartState.loading,
      onDownload,
    };
  };

  const handleExport = async (type: ChartType) => {
    const params = filterParams.value;
    const currentTabConfig = tabMap.find((tab) => tab.id === activeTab.value);
    const chartConfig = currentTabConfig?.charts[type];

    if (!chartConfig?.exportApi) {
      createMessage.warning('该图表不支持导出');
      return;
    }

    exportLoading[type] = true;
    try {
      await exportUtil(chartConfig.exportApi(params));
    } catch (error) {
      createMessage.error('导出失败');
      console.error('导出失败:', error);
    } finally {
      exportLoading[type] = false;
    }
  };

  const charts = computed(() => {
    const currentTabConfig = tabMap.find((tab) => tab.id === activeTab.value);
    const baseTitle = currentTabConfig?.title || '';

    const chartConfigs = [
      {
        key: ChartType.TREND,
        colSpan: getChartColSpan({ tabId: activeTab.value, chartType: ChartType.TREND }),
      },
      {
        key: ChartType.DEPARTMENT,
        colSpan: getChartColSpan({ tabId: activeTab.value, chartType: ChartType.DEPARTMENT }),
      },
      {
        key: ChartType.PRESCRIPTION,
        colSpan: getChartColSpan({ tabId: activeTab.value, chartType: ChartType.PRESCRIPTION }),
      },
      {
        key: ChartType.DOCTOR,
        colSpan: getChartColSpan({ tabId: activeTab.value, chartType: ChartType.DOCTOR }),
      },
    ];

    return chartConfigs
      .filter(
        (c) =>
          currentTabConfig?.charts[c.key] &&
          isChartVisible({ tabId: activeTab.value, chartType: c.key }),
      )
      .map((chart) => {
        return {
          ...chart,
          downloadLoading: exportLoading[chart.key],
          props: createChartProps(chart.key, baseTitle, () => handleExport(chart.key)),
        };
      });
  });

  // 刷新 Top Card 数据
  const [perPersonCardLoading, setPerPersonCardLoading] = useToggle(false);
  const [averageCardLoading, setAverageCardLoading] = useToggle(false);

  async function getTopCardDataRefresh() {
    const params = filterParams.value;

    const promises = visibleTabs.value.map(async (tab) => {
      try {
        // 这里需要根据 tab.key 设置对应的 loading 状态
        // 由于原来的 loading 状态是固定的，这里保持原有逻辑
        if (tab.key === 'perPerson') {
          setPerPersonCardLoading(true);
        } else if (tab.key === 'average') {
          setAverageCardLoading(true);
        }

        const result = await tab.cardApi(params);

        // 更新对应的数据
        statsData[tab.cardDataKeys.value] = keepDecimalString(result.value);
        statsData[tab.cardDataKeys.contemporaryValue] = keepDecimalString(result.contemporaryValue);
        statsData[tab.cardDataKeys.growthRate] = keepDecimalString(result.growthRate);
      } catch (error) {
        createMessage.error(`获取${tab.title}数据失败`);
        console.error(`获取${tab.title}数据失败:`, error);
      } finally {
        if (tab.key === 'perPerson') {
          setPerPersonCardLoading(false);
        } else if (tab.key === 'average') {
          setAverageCardLoading(false);
        }
      }
    });

    await Promise.all(promises);
  }

  // 统一的数据刷新函数
  const refreshChartData = async (type: ChartType, apiCall: () => Promise<any>) => {
    try {
      const currentFetchKey = fetchKey.value;
      chartData[type].loading = true;
      const result = await apiCall();
      if (currentFetchKey === fetchKey.value) {
        chartData[type].data = result;
      } else {
        console.log(
          'Data fetch key has changed, not updating chart data.',
          currentFetchKey,
          fetchKey.value,
        );
      }
    } catch (error) {
      createMessage.error('获取数据失败');
      console.error('获取数据失败:', error);
    } finally {
      chartData[type].loading = false;
    }
  };

  // 刷新所有数据
  const refreshData = async (refreshTopCard = true) => {
    if (refreshTopCard) {
      getTopCardDataRefresh();
    }
    fetchKey.value = new Date().valueOf();
    const params = filterParams.value;
    const currentTabConfig = tabMap.find((tab) => tab.id === activeTab.value);

    if (currentTabConfig) {
      Object.entries(currentTabConfig.charts).forEach(([chartType, chartConfig]) => {
        if (
          chartConfig &&
          isChartVisible({ tabId: activeTab.value, chartType: chartType as ChartType })
        ) {
          refreshChartData(chartType as ChartType, () => chartConfig.api(params));
        }
      });
    }
  };

  watch(activeTab, () => {
    refreshData(false);
  });

  // 重置筛选条件
  const resetFilter = () => {
    nextTick(refreshData);
  };

  // 页面加载时获取数据
  onMounted(() => {
    refreshData();
  });
</script>

<template>
  <div class="h-full flex flex-col gap-2 bg-[#f5f7fa] overflow-hidden">
    <!-- 筛选条件区 -->
    <div class="bg-white p-4 rounded-lg">
      <CommonFilter v-model="filterParams" @search="refreshData(true)" @reset="resetFilter" />
    </div>

    <!-- 统计卡片区 -->
    <div class="grid grid-cols-4 gap-2">
      <StatCard
        v-for="tab in visibleTabs"
        :key="tab.id"
        :active="activeTab === tab.id"
        :title="tab.title"
        :value="statsData[tab.cardDataKeys.value]"
        :compare-value="statsData[tab.cardDataKeys.contemporaryValue]"
        :rate="statsData[tab.cardDataKeys.growthRate]"
        :icon="tab.icon"
        :loading="tab.key === 'perPerson' ? perPersonCardLoading : averageCardLoading"
        @click="activeTab = tab.id"
      />
    </div>

    <!-- 图表区域 -->
    <div class="flex-1 grid grid-cols-24 gap-2 min-h-0 bg-white">
      <div
        v-for="chart in charts"
        :key="chart.key"
        class="p-4 rounded-lg h-[400px]"
        :style="{
          gridColumn: `span ${chart?.colSpan ?? 12} / span ${chart?.colSpan ?? 12}`,
        }"
      >
        <ChartTable v-bind="chart.props" :download-loading="chart.downloadLoading" class="h-full" />
      </div>
    </div>
  </div>
</template>
