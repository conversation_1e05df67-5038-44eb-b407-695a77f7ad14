<template>
  <div class="w-full h-full flex">
    <div class="w-560px h-full flex flex-col">
      <div class="px-4 pt-4">
        <BasicTitle span> {{ title }} </BasicTitle>
      </div>
      <div class="flex-1 of-hidden">
        <BasicTable @register="registerTable" />
      </div>
    </div>
    <div class="py-4">
      <i class="block h-full w-0 border-l border-gray-200"></i>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { BasicTable, useTable } from '@hiip/internal/components/Table';
  import type { PatientItem } from '/@/api/adt-list';
  import { getPage } from '/@/api/adt-list';
  import { getInpatientArea, getVisitDept } from '/@/api/medic-order-list';
  import { BasicTitle } from '@hiip/internal/components/Basic';

  defineProps<{
    title?: string;
  }>();

  const emit = defineEmits<{
    (e: 'select', record: PatientItem): void;
  }>();

  const [registerTable] = useTable({
    api: getPage,
    showIndexColumn: true,
    inset: true,
    useSearchForm: true,
    immediate: true,
    formConfig: {
      rowProps: {
        gutter: 16,
      },
      schemas: [
        {
          field: 'inpatientNo',
          component: 'Input',
          label: '',
          colProps: { span: 12 },
          componentProps: {
            placeholder: '住院号',
          },
        },
        {
          field: 'outpatientNo',
          component: 'Input',
          label: '',
          colProps: { span: 12 },
          componentProps: {
            placeholder: '门急诊号',
          },
        },
        {
          field: 'patientName',
          component: 'Input',
          label: '',
          colProps: { span: 12 },
          componentProps: {
            placeholder: '患者姓名',
          },
        },
        {
          field: 'visitDept',
          component: 'ApiSelect',
          label: '',
          colProps: { span: 12 },
          componentProps: {
            api: getVisitDept,
            placeholder: '就诊科室',
          },
        },
        {
          field: 'inpatientArea',
          component: 'ApiSelect',
          label: '',
          colProps: { span: 12 },
          componentProps: {
            api: getInpatientArea,
            placeholder: '住院病区',
          },
        },

        {
          field: 'date',
          component: 'RangePicker',
          label: '',
          colProps: { span: 12 },
          componentProps: {
            placeholder: ['开始时间', '结束时间'],
          },
        },
      ],
      fieldMapToTime: [['date', ['startDate', 'endDate']]],
      actionColOptions: {
        span: 24,
      },
      showAdvancedButton: false,
    },
    customRow: (record) => {
      return {
        class: 'cursor-pointer',
        onClick: handleRow.bind(null, record),
      };
    },
    columns: [
      {
        dataIndex: 'inpatientNo',
        title: '住院号',
        width: 120,
      },
      {
        dataIndex: 'patientName',
        title: '患者姓名',
        width: 80,
      },
      {
        dataIndex: 'visitDept',
        title: '就诊科室',
        width: 120,
      },
      {
        dataIndex: 'inpatientArea',
        title: '住院病区',
        width: 150,
      },
      {
        dataIndex: 'cardNo',
        title: '卡号',
        width: 120,
      },
      {
        dataIndex: 'doctor',
        title: '医生',
        width: 80,
      },
      {
        dataIndex: 'nurse',
        title: '护士',
        width: 80,
      },
    ],
  });

  function handleRow(record: PatientItem) {
    emit('select', record);
  }
</script>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container .ant-form {
      padding: 16px 16px 6px;
    }
  }
</style>
